<view class="container">
  <view class="title">向好友分享猎优企</view>
  <view class="card-container">
    <view class="main-image">
      <image src="/images/share/share.jpg" mode="aspectFill" class="preset-image">预定图片</image>
    </view>
    <view class="card-row">
      <view class="card-left">
        <view style="display:flex;align-items:center;">
          <image class="avatar-circle" src="{{avatarUrl}}"></image>
          <view class="nickname-block">
            <text class="nickname">{{nickname}}</text>
            <text class="recommend">向您推荐</text>
          </view>
        </view>
        <view class="desc">助力优质企业资产流转</view>
      </view>
      <view class="qrcode-container">
        <image wx:if="{{qrcodeUrl}}" class="qrcode" src="{{qrcodeUrl}}" mode="aspectFit"></image>
        <view wx:else class="qrcode-loading" bindtap="retryGenerateQrcode">
          <text>{{qrcodeUrl === null ? '点击重试' : '二维码生成中...'}}</text>
        </view>
      </view>
    </view>
  </view>
  <view class="actions">
    <button class="save-btn" bindtap="onSaveImage">保存图片</button>
    <button class="share-btn" open-type="share">直接分享</button>
  </view>
  <canvas canvas-id="shareCanvas" style="width:600rpx;height:800rpx;display:none;"></canvas>
</view>