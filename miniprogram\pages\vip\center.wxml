<view class="vip-center-container">
  <!-- 用户信息区 -->
  <view class="user-info">
    <image class="avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill"></image>
    <view class="user-details">
      <view class="nickname">{{userInfo.nickName || '昵称'}}</view>
      <view class="user-id">ID: {{userInfo.id || 'XXXXXXXXXX'}}</view>
    </view>
    <view class="vip-info">
      <view class="vip-label">年度会员</view>
      <view class="vip-expire">有效期至{{vipInfo.expireDate || '2026年5月12日'}}</view>
    </view>
  </view>

  <!-- 轮播图区 -->
  <view class="swiper-section">
    <swiper class="vip-swiper" indicator-dots="true" autoplay="true" interval="3000" duration="500" style="height:200rpx;">
      <block wx:for="{{banners}}" wx:key="index">
        <swiper-item>
          <image src="{{item}}" class="banner-img" mode="aspectFill"></image>
        </swiper-item>
      </block>
    </swiper>
  </view>

  <!-- 会员权益卡片区 -->
  <view class="vip-benefit-section">
    <swiper class="benefit-swiper" indicator-dots="true" circular="true" autoplay="{{false}}" style="height:630rpx;" display-multiple-items="1.5">
      <block wx:for="{{vipProducts}}" wx:key="id">
        <swiper-item>
          <view class="benefit-card">
            <view class="benefit-title">{{item.name}}</view>
            <view class="benefit-list">
              <view class="benefit-item" wx:for="{{item.benefits}}" wx:key="index">
                权益{{index+1}}：{{item.benefits[index]}}
              </view>
            </view>
            <button class="renew-btn">{{item.buttonText || '会员续费'}}</button>
          </view>
        </swiper-item>
      </block>
    </swiper>
  </view>

  <button class="more-vip-btn">更多会员产品</button>
</view> 