.vip-center-container {
  display: flex;
  flex-direction: column;
  background: #fff;
  min-height: 100vh;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx 24rpx 24rpx;
  border-bottom: 2rpx solid #e5e5e5;
  background: #f9f9f9;
}
.avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  background: #17637a;
  margin-right: 32rpx;
}
.user-details {
  flex: 1;
}
.nickname {
  font-size: 38rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.user-id {
  font-size: 26rpx;
  color: #666;
}
.vip-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.vip-label {
  font-size: 36rpx;
  color: #222;
  font-weight: bold;
}
.vip-expire {
  font-size: 24rpx;
  color: #888;
  margin-top: 8rpx;
}

.swiper-section {
  margin: 0 0 24rpx 0;
  background: #e5e5e5;
  height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.vip-swiper {
  width: 100%;
  height: 200rpx;
  min-height: 200rpx;
  background: #e5e5e5;
}
.banner-img {
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
}

.vip-benefit-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 24rpx 0 0 0;
  position: relative;
}
.benefit-swiper {
  width: 100%;
  height: 630rpx;
  min-height: 630rpx;
  background: transparent;
  display: flex;
  align-items: center;
  border: 10px solid #e5e5e5;
  border-radius: 24rpx;
  box-sizing: border-box;
}
.benefit-card {
  width: 50vw;
  min-width: 50vw;
  max-width: 50vw;
  flex: none;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 24rpx rgba(23,99,122,0.12);
  border: 4rpx solid #17637a;
  padding: 48rpx 32rpx;
  margin: 0 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 520rpx;
  max-height: 630rpx;
  overflow-y: auto;
  transition: box-shadow 0.2s, border 0.2s;
}
.benefit-title {
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 18rpx;
  color: #17637a;
}
.benefit-list {
  width: 100%;
  margin-bottom: 24rpx;
}
.benefit-item {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 12rpx;
}
.renew-btn {
  width: 60%;
  margin-top: 12rpx;
  background: #17637a;
  color: #fff;
  font-size: 30rpx;
  border-radius: 8rpx;
  padding: 16rpx 0;
}
.benefit-arrow {
  position: absolute;
  top: 50%;
  width: 48rpx;
  height: 48rpx;
  background: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #aaa;
  z-index: 2;
  box-shadow: 0 2rpx 8rpx rgba(23,99,122,0.08);
  cursor: pointer;
}
.benefit-arrow.left {
  left: 0;
  transform: translateY(-50%);
}
.benefit-arrow.right {
  right: 0;
  transform: translateY(-50%);
}
.more-vip-btn {
  width: 80%;
  margin: 40rpx auto 0 auto;
  background: #fff;
  color: #17637a;
  font-size: 32rpx;
  border: 2rpx solid #17637a;
  border-radius: 8rpx;
  padding: 18rpx 0;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(23,99,122,0.06);
} 