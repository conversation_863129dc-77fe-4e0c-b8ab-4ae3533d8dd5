商城页面结构：
顶部搜索：
- 搜索框样式同首页
- 右侧购物车入口
- 购物车角标：红点数字

分类导航：
- 双层分类
- 一级分类：
  - 横向滚动
  - 选中态底部条
  
- 二级分类：
  - 横向滚动标签
  - 标签样式：
    - 圆角胶囊
    - 选中态填充色

商品列表：
- 布局：双列瀑布流
- 商品卡片：
  - 宽度：(屏幕宽-36pt)/2
  - 间距：12pt
  - 圆角：8pt
  - 阴影：轻微
  
- 卡片内容：
  - 商品图：
    - 宽高比：1:1
    - 圆角：上方4pt
    
  - 商品信息：
    - 标题：双行限制
    - 价格：主题色
    - 销量：灰色小字
    - 店铺：单行
    
  - 购买按钮：
    - 小尺寸圆角
    - 主题色背景

筛选功能：
- 顶部筛选栏
- 排序：综合/销量/价格
- 筛选器：
  - 价格区间
  - 商品属性
  - 优惠活动

商品详情页：
- 轮播图：
  - 高度：350pt
  - 页码指示器
  
- 价格区：
  - 现价/原价
  - 促销标签
  
- 商品信息：
  - 标题
  - 描述
  - 规格选择
  - 服务标签
  
- 店铺信息：
  - 店铺头像
  - 店铺名称
  - 关注按钮
  
- 详情区：
  - Tab切换
  - 图文详情
  - 规格参数
  - 售后说明
  
- 底部操作栏：
  - 购物车
  - 客服
  - 加入购物车
  - 立即购买