FROM node:18-alpine

# 安装Python和编译工具
RUN apk add --no-cache python3 make g++ gcc

# 创建工作目录
WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装依赖前切换npm源为淘宝镜像，加速安装
RUN npm config set registry https://registry.npmmirror.com

# 安装依赖
RUN npm install --production

# 复制所有文件
COPY . .

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3000
ENV USE_MYSQL=true

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["node", "server.js"]
