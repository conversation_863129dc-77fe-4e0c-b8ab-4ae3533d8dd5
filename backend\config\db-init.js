const mysql = require('mysql2/promise');
const config = require('./config');

async function initializeDatabase() {
  try {
    // 创建数据库连接
    const connection = await mysql.createConnection({
      host: config.db.host,
      user: config.db.user,
      password: config.db.password
    });

    // 创建数据库（如果不存在）
    await connection.query(`CREATE DATABASE IF NOT EXISTS ${config.db.database}`);
    await connection.query(`USE ${config.db.database}`);

    // 创建用户表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(20) PRIMARY KEY,
        username VARCHAR(50) NOT NULL,
        password VARCHAR(100) NOT NULL,
        nickname VA<PERSON>HAR(50),
        avatar VARCHAR(255),
        gender ENUM('male', 'female', 'other'),
        phone VARCHAR(20),
        email VARCHAR(100),
        referrerId VARCHAR(20),
        createTime BIGINT,
        updateTime BIGINT
      )
    `);

    // 创建帖子表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS posts (
        id VARCHAR(24) PRIMARY KEY,
        userId VARCHAR(20) NOT NULL,
        content TEXT,
        images TEXT,
        video VARCHAR(255),
        topic VARCHAR(100),
        location VARCHAR(255),
        isPublic BOOLEAN DEFAULT true,
        allowComment BOOLEAN DEFAULT true,
        allowForward BOOLEAN DEFAULT true,
        linkedProducts TEXT,
        region VARCHAR(100),
        userInfo TEXT,
        createTime BIGINT,
        likeCount INT DEFAULT 0,
        commentCount INT DEFAULT 0,
        shareCount INT DEFAULT 0,
        FOREIGN KEY (userId) REFERENCES users(id)
      )
    `);

    // 创建评论表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS comments (
        id VARCHAR(24) PRIMARY KEY,
        postId VARCHAR(24) NOT NULL,
        userId VARCHAR(20) NOT NULL,
        content TEXT NOT NULL,
        createTime BIGINT,
        FOREIGN KEY (postId) REFERENCES posts(id),
        FOREIGN KEY (userId) REFERENCES users(id)
      )
    `);

    // 创建点赞表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS likes (
        id VARCHAR(24) PRIMARY KEY,
        postId VARCHAR(24) NOT NULL,
        userId VARCHAR(20) NOT NULL,
        createTime BIGINT,
        FOREIGN KEY (postId) REFERENCES posts(id),
        FOREIGN KEY (userId) REFERENCES users(id)
      )
    `);

    // 创建群组表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS groups (
        id VARCHAR(32) PRIMARY KEY COMMENT '群组ID',
        name VARCHAR(50) NOT NULL COMMENT '群组名称',
        avatar VARCHAR(255) DEFAULT '/images/icons/group-default.png' COMMENT '群组头像',
        description TEXT COMMENT '群组描述',
        announcement TEXT COMMENT '群公告',
        creatorId VARCHAR(32) NOT NULL COMMENT '创建者ID',
        isPublic BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否公开群组',
        memberCount INT NOT NULL DEFAULT 1 COMMENT '成员数量',
        createTime BIGINT NOT NULL COMMENT '创建时间',
        updateTime BIGINT NOT NULL COMMENT '更新时间',
        INDEX idx_creator (creatorId),
        INDEX idx_create_time (createTime)
      )
    `);

    // 创建群组成员表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS group_members (
        id INT AUTO_INCREMENT PRIMARY KEY,
        groupId VARCHAR(32) NOT NULL COMMENT '群组ID',
        userId VARCHAR(32) NOT NULL COMMENT '用户ID',
        role ENUM('owner', 'admin', 'member') NOT NULL DEFAULT 'member' COMMENT '成员角色',
        nickname VARCHAR(50) COMMENT '群内昵称',
        joinTime BIGINT NOT NULL COMMENT '加入时间',
        INDEX idx_group (groupId),
        INDEX idx_user (userId),
        UNIQUE KEY uk_group_user (groupId, userId)
      )
    `);

    // 创建群消息表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS group_messages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        groupId VARCHAR(32) NOT NULL COMMENT '群组ID',
        senderId VARCHAR(32) NOT NULL COMMENT '发送者ID',
        content TEXT NOT NULL COMMENT '消息内容',
        type VARCHAR(20) NOT NULL DEFAULT 'text' COMMENT '消息类型：text-文本，image-图片',
        createTime BIGINT NOT NULL COMMENT '创建时间',
        INDEX idx_group (groupId),
        INDEX idx_sender (senderId),
        INDEX idx_create_time (createTime)
      )
    `);

    // 创建公司信息表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS company_info (
        id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
        company_name VARCHAR(100) NOT NULL COMMENT '公司名称',
        company_description TEXT COMMENT '公司简介',
        company_address VARCHAR(255) COMMENT '公司地址',
        company_phone VARCHAR(50) COMMENT '公司电话',
        company_email VARCHAR(100) COMMENT '公司邮箱',
        company_website VARCHAR(255) COMMENT '公司网站',
        business_hours VARCHAR(100) COMMENT '营业时间',
        company_logo VARCHAR(255) COMMENT '公司Logo',
        contact_person VARCHAR(50) COMMENT '联系人',
        fax VARCHAR(50) COMMENT '传真号码',
        postal_code VARCHAR(20) COMMENT '邮政编码',
        company_type VARCHAR(50) COMMENT '公司类型',
        established_date DATE COMMENT '成立日期',
        registration_number VARCHAR(100) COMMENT '注册号码',
        social_credit_code VARCHAR(100) COMMENT '统一社会信用代码',
        legal_representative VARCHAR(50) COMMENT '法定代表人',
        registered_capital DECIMAL(15,2) COMMENT '注册资本',
        business_scope TEXT COMMENT '经营范围',
        company_status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '公司状态',
        createTime BIGINT NOT NULL COMMENT '创建时间',
        updateTime BIGINT NOT NULL COMMENT '更新时间',
        INDEX idx_company_name (company_name),
        INDEX idx_create_time (createTime),
        INDEX idx_update_time (updateTime)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司信息表'
    `);

    // 创建用户留言表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS user_feedback (
        id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
        user_id VARCHAR(20) COMMENT '用户ID（可为空，支持未登录用户留言）',
        name VARCHAR(50) NOT NULL COMMENT '留言人姓名',
        contact VARCHAR(20) NOT NULL COMMENT '联系方式（手机号）',
        message TEXT NOT NULL COMMENT '留言内容',
        status ENUM('pending', 'processing', 'resolved') DEFAULT 'pending' COMMENT '处理状态',
        admin_reply TEXT COMMENT '管理员回复',
        create_time BIGINT NOT NULL COMMENT '创建时间',
        update_time BIGINT NOT NULL COMMENT '更新时间',
        INDEX idx_user_id (user_id),
        INDEX idx_status (status),
        INDEX idx_create_time (create_time),
        INDEX idx_contact (contact)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户留言表'
    `);

    await connection.end();
    console.log('数据库初始化完成');
    return true;
  } catch (error) {
    console.error('数据库初始化失败:', error);
    throw error;
  }
}

module.exports = {
  initializeDatabase
};