/**
 * 消息数据检查脚本
 * 用于检查数据库中的消息数据
 */
require('dotenv').config();
const mysql = require('mysql2/promise');

// 创建数据库连接池
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'molI2505$',
  database: process.env.DB_NAME || 'lieyouqi',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// 查询函数
async function query(sql, params) {
  try {
    const [rows] = await pool.execute(sql, params);
    return rows;
  } catch (error) {
    console.error('查询错误:', error.message);
    throw error;
  }
}

// 检查消息表
async function checkMessagesTable() {
  try {
    console.log('检查消息表结构...');
    const tables = await query(`
      SHOW TABLES LIKE 'messages'
    `);
    
    if (tables.length === 0) {
      console.error('错误: 消息表不存在!');
      return false;
    }
    
    console.log('消息表存在，检查表结构...');
    const columns = await query(`
      SHOW COLUMNS FROM messages
    `);
    
    console.log('消息表列信息:');
    columns.forEach(column => {
      console.log(`- ${column.Field}: ${column.Type} ${column.Null === 'YES' ? '(可空)' : '(非空)'}`);
    });
    
    return true;
  } catch (error) {
    console.error('检查消息表结构失败:', error.message);
    return false;
  }
}

// 检查消息数据
async function checkMessagesData() {
  try {
    console.log('\n检查消息数据...');
    const count = await query(`
      SELECT COUNT(*) as count FROM messages
    `);
    
    console.log(`消息表中有 ${count[0].count} 条记录`);
    
    if (count[0].count === 0) {
      console.warn('警告: 消息表中没有数据!');
      return;
    }
    
    // 获取最新的10条消息
    console.log('\n最新的10条消息:');
    const messages = await query(`
      SELECT 
        m.*,
        u1.nickname as sender_nickname,
        u2.nickname as receiver_nickname
      FROM messages m
      LEFT JOIN users u1 ON m.senderId = u1.id
      LEFT JOIN users u2 ON m.receiverId = u2.id
      ORDER BY m.createTime DESC
      LIMIT 10
    `);
    
    messages.forEach((msg, index) => {
      console.log(`\n消息 #${index + 1}:`);
      console.log(`- ID: ${msg.id}`);
      console.log(`- 发送者ID: ${msg.senderId} (${msg.sender_nickname || '未知'})`);
      console.log(`- 接收者ID: ${msg.receiverId} (${msg.receiver_nickname || '未知'})`);
      console.log(`- 内容: ${msg.content}`);
      console.log(`- 类型: ${msg.type}`);
      console.log(`- 创建时间: ${new Date(msg.createTime).toLocaleString()}`);
      console.log(`- 是否已读: ${msg.isRead ? '是' : '否'}`);
    });
    
    // 检查用户ID是否存在
    console.log('\n检查用户ID是否存在...');
    const senderIds = [...new Set(messages.map(msg => msg.senderId))];
    const receiverIds = [...new Set(messages.map(msg => msg.receiverId))];
    const userIds = [...new Set([...senderIds, ...receiverIds])];
    
    console.log(`消息中涉及的用户ID: ${userIds.join(', ')}`);
    
    for (const userId of userIds) {
      const user = await query(`
        SELECT id, username, nickname FROM users WHERE id = ?
      `, [userId]);
      
      if (user.length === 0) {
        console.warn(`警告: 用户ID ${userId} 在用户表中不存在!`);
      } else {
        console.log(`用户ID ${userId}: ${user[0].nickname} (${user[0].username})`);
      }
    }
  } catch (error) {
    console.error('检查消息数据失败:', error.message);
  }
}

// 主函数
async function main() {
  try {
    console.log('开始检查消息数据...\n');
    
    // 检查消息表结构
    const tableExists = await checkMessagesTable();
    if (!tableExists) {
      console.error('消息表不存在，无法继续检查');
      process.exit(1);
    }
    
    // 检查消息数据
    await checkMessagesData();
    
    console.log('\n检查完成!');
  } catch (error) {
    console.error('检查过程中发生错误:', error);
  } finally {
    // 关闭连接池
    await pool.end();
  }
}

// 执行主函数
main();
