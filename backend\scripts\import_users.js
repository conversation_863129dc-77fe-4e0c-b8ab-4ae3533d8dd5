const fs = require('fs');
const mysql = require('mysql2/promise');

(async () => {
  // 数据库连接配置
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'molI2505$',
    database: 'lieyouqi',
    charset: 'utf8mb4',
  });

  // 读取备份文件内容
  const sql = fs.readFileSync('backend/users_backup.sql', 'utf8');
  // 匹配所有数据组
  const match = sql.match(/INSERT INTO `users` VALUES (.*);/s);
  if (!match) {
    console.log('未找到INSERT语句');
    process.exit(1);
  }
  // 拆分每组数据
  const valuesStr = match[1];
  // 用正则分割每个()，支持嵌套逗号
  const rows = [];
  let depth = 0, start = 0;
  for (let i = 0; i < valuesStr.length; i++) {
    if (valuesStr[i] === '(') {
      if (depth === 0) start = i;
      depth++;
    } else if (valuesStr[i] === ')') {
      depth--;
      if (depth === 0) {
        rows.push(valuesStr.slice(start, i + 1));
      }
    }
  }

  // 新表字段顺序
  const fields = ['id','username','password','nickname','avatar','phone','email','gender','country','province','city','openid','role','createTime','account_username','account_password','password_salt','user_type','updateTime'];

  let success = 0, fail = 0;
  for (const row of rows) {
    try {
      // 直接拼接SQL
      const sql = `INSERT INTO users (${fields.join(',')}) VALUES ${row}`;
      await connection.query(sql);
      success++;
    } catch (e) {
      fail++;
      console.log('插入失败:', e.message);
    }
  }
  console.log(`导入完成，成功: ${success}，失败: ${fail}`);
  await connection.end();
})(); 