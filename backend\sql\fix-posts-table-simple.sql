-- 使用数据库
USE lieyouqi;

-- 检查posts表是否存在，如果存在则先备份
CREATE TABLE IF NOT EXISTS posts_backup AS SELECT * FROM posts;

-- 创建一个新的posts表，只包含必要的字段
DROP TABLE IF EXISTS posts;
CREATE TABLE posts (
  id VARCHAR(24) NOT NULL PRIMARY KEY,
  userId VARCHAR(20) NULL,
  content TEXT NULL,
  images TEXT NULL,
  userInfo TEXT NULL,
  createTime BIGINT NULL,
  likeCount INT DEFAULT 0,
  commentCount INT DEFAULT 0,
  shareCount INT DEFAULT 0,
  video VARCHAR(255) NULL,
  topic VARCHAR(100) NULL
);

-- 从备份表恢复数据
INSERT INTO posts (id, userId, content, images, userInfo, createTime, likeCount, commentCount, shareCount, video, topic)
SELECT 
  id, 
  userId, 
  content, 
  images, 
  userInfo, 
  createTime, 
  IFNULL(likeCount, 0), 
  IFNULL(commentCount, 0), 
  IFNULL(shareCount, 0), 
  video, 
  topic
FROM posts_backup;

-- 查看表结构
DESCRIBE posts;
