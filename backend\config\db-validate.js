const mysql = require('mysql2/promise');
const config = require('./config');

async function validateDatabase() {
  try {
    const connection = await mysql.createConnection({
      host: config.db.host,
      user: config.db.user,
      password: config.db.password,
      database: config.db.database
    });

    // 检查必要的表是否存在
    const [tables] = await connection.query('SHOW TABLES');
    const tableNames = tables.map(table => Object.values(table)[0]);

    const requiredTables = ['users', 'posts', 'comments', 'likes', 'company_info'];
    const missingTables = requiredTables.filter(table => !tableNames.includes(table));

    await connection.end();

    if (missingTables.length > 0) {
      throw new Error(`缺少必要的表: ${missingTables.join(', ')}`);
    }

    return true;
  } catch (error) {
    console.error('数据库验证失败:', error);
    throw error;
  }
}

module.exports = {
  validateDatabase
};