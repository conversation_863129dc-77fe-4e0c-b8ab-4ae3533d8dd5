CREATE TABLE IF NOT EXISTS groups (
  id VARCHAR(32) PRIMARY KEY COMMENT '群组ID',
  name VARCHAR(50) NOT NULL COMMENT '群组名称',
  avatar VARCHAR(255) DEFAULT '/images/icons/group-default.png' COMMENT '群组头像',
  description TEXT COMMENT '群组描述',
  announcement TEXT COMMENT '群公告',
  creatorId VARCHAR(32) NOT NULL COMMENT '创建者ID',
  isPublic BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否公开群组',
  memberCount INT NOT NULL DEFAULT 1 COMMENT '成员数量',
  createTime BIGINT NOT NULL COMMENT '创建时间',
  updateTime BIGINT NOT NULL COMMENT '更新时间',
  INDEX idx_creator (creatorId),
  INDEX idx_create_time (createTime)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='群组表';

CREATE TABLE IF NOT EXISTS group_members (
  id INT AUTO_INCREMENT PRIMARY KEY,
  groupId VARCHAR(32) NOT NULL COMMENT '群组ID',
  userId VARCHAR(32) NOT NULL COMMENT '用户ID',
  role ENUM('owner', 'admin', 'member') NOT NULL DEFAULT 'member' COMMENT '成员角色',
  nickname VARCHAR(50) COMMENT '群内昵称',
  joinTime BIGINT NOT NULL COMMENT '加入时间',
  INDEX idx_group (groupId),
  INDEX idx_user (userId),
  UNIQUE KEY uk_group_user (groupId, userId)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='群组成员表';

CREATE TABLE IF NOT EXISTS group_messages (
  id INT AUTO_INCREMENT PRIMARY KEY,
  groupId VARCHAR(32) NOT NULL COMMENT '群组ID',
  senderId VARCHAR(32) NOT NULL COMMENT '发送者ID',
  content TEXT NOT NULL COMMENT '消息内容',
  type VARCHAR(20) NOT NULL DEFAULT 'text' COMMENT '消息类型：text-文本，image-图片',
  createTime BIGINT NOT NULL COMMENT '创建时间',
  INDEX idx_group (groupId),
  INDEX idx_sender (senderId),
  INDEX idx_create_time (createTime)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='群消息表';