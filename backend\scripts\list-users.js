const db = require('../config/db');

async function listAllUsers() {
  try {
    const users = await db.query('SELECT id, username, nickname, gender, phone, email, createTime FROM users');
    console.log('所有用户列表：');
    console.log('----------------------------------------');
    users.forEach(user => {
      console.log(`ID: ${user.id}`);
      console.log(`用户名: ${user.username}`);
      console.log(`昵称: ${user.nickname || '未设置'}`);
      console.log(`性别: ${user.gender || '未设置'}`);
      console.log(`电话: ${user.phone || '未设置'}`);
      console.log(`邮箱: ${user.email || '未设置'}`);
      console.log(`创建时间: ${new Date(user.createTime).toLocaleString()}`);
      console.log('----------------------------------------');
    });
    console.log(`总计: ${users.length} 个用户`);
  } catch (error) {
    console.error('查询用户列表失败:', error);
  } finally {
    process.exit();
  }
}

listAllUsers(); 