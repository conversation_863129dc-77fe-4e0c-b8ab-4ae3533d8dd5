/**
 * 测试关于我们页面的数据
 */
const CompanyInfo = require('../models/CompanyInfo');

async function testAboutPageData() {
  console.log('=== 测试关于我们页面数据 ===\n');

  try {
    // 1. 检查公司信息表是否存在数据
    console.log('1. 检查公司信息表数据');
    const allCompanies = await CompanyInfo.findAll();
    console.log(`找到 ${allCompanies.length} 条公司信息记录`);

    if (allCompanies.length === 0) {
      console.log('❌ 没有找到公司信息，需要先插入数据');
      console.log('\n请在数据库中执行以下SQL命令：');
      console.log(`
INSERT INTO company_info (
  company_name,
  company_description,
  company_address,
  company_phone,
  company_email,
  company_website,
  business_hours,
  contact_person,
  company_type,
  established_date,
  company_status,
  createTime,
  updateTime
) VALUES (
  '上海晚歌集团',
  '上海晚歌企业发展集团，初创于2010年。经过11年的发展，历享凡贸易、晚歌家纺、鑫策财税等不同的业务项目后组建集团公司，并不断扩大集团业务范围，尤其是最近三年，在公司全体同仁的努力，政府机关、社会各界人士的支持，以及广大客户的信任下，发展速度日渐加快。

当前集团旗下已逐渐形成包括（运营主体为"鑫策(上海)企业登记代理有限公司"的）工商税务代理、（运营主体为"上海国巹企业管理有限公司"的）知识产权、（运营主体为"上海鑫心征信服务有限公司"的）企业资质、（运营主体为"上海今嘉人力资源有限公司"的）人事代理、（运营主体为"猎优企（上海）企业发展有限公司"的）企业转让等主要业务板块；团队规模达到近50人；办公场地包括总部及工业园区分部室等超过700平米；积累各项业务的客户超过5000家，在行业中已具备相当的知名度和影响力。

新的十年，公司提出更高的目标和发展战略，力将进一步升级公司现有业务模型，构建新的商业模式，勇做行业创新先行者，建立行业业务标准规范；筹备业务将延伸至涵盖金融财务咨询、企业管理咨询、人力资源服务、融资上市服务等全方位企业服务。

有鉴于此，集团需要也欢迎社会各界有识有志之士加盟，群策群力，不断实现新的发展目标；公司深知人才对企业发展的重要性，相比很多企业只关心你能为它做什么，我们非常在意公司能如何帮助你成长。

相信集团的未来一定会更加美好！也愿你的未来同样精彩！',
  '上海市黄浦区陆家浜路1332号南开大厦805室',
  '400-788-3600',
  '<EMAIL>',
  'https://www.wangegroup.com',
  '周一至周五 9:00-18:00',
  '客服中心',
  '集团公司',
  '2010-01-01',
  'active',
  UNIX_TIMESTAMP() * 1000,
  UNIX_TIMESTAMP() * 1000
);
      `);
      return;
    }

    // 2. 显示第一条公司信息（关于我们页面会使用的数据）
    console.log('\n2. 关于我们页面将显示的公司信息：');
    const companyInfo = allCompanies[0];
    console.log('✓ 公司名称:', companyInfo.company_name);
    console.log('✓ 公司类型:', companyInfo.company_type);
    console.log('✓ 成立时间:', companyInfo.established_date);
    console.log('✓ 公司地址:', companyInfo.company_address);
    console.log('✓ 联系电话:', companyInfo.company_phone);
    console.log('✓ 邮箱地址:', companyInfo.company_email);
    console.log('✓ 官方网站:', companyInfo.company_website);
    console.log('✓ 营业时间:', companyInfo.business_hours);
    console.log('✓ 联系人:', companyInfo.contact_person);
    console.log('✓ 企业状态:', companyInfo.company_status);
    
    // 显示公司简介的前100个字符
    const description = companyInfo.company_description || '';
    console.log('✓ 公司简介:', description.length > 100 ? description.substring(0, 100) + '...' : description);

    // 3. 测试API路由
    console.log('\n3. API路由测试');
    console.log('✓ GET /api/company/info - 获取公司信息（无参数）');
    console.log('✓ GET /api/company/list - 获取公司信息列表');
    console.log('✓ 前端页面路径: /pages/about/index');

    console.log('\n=== 测试完成 ===');
    console.log('✅ 关于我们页面数据准备就绪！');
    console.log('\n📱 使用说明：');
    console.log('1. 在小程序中进入"我的"页面');
    console.log('2. 点击"关于我们"菜单项');
    console.log('3. 查看公司信息是否正确显示');
    console.log('4. 测试电话拨打、地址复制等功能');

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testAboutPageData().then(() => {
    process.exit(0);
  }).catch(err => {
    console.error('脚本执行失败:', err);
    process.exit(1);
  });
}

module.exports = { testAboutPageData };
