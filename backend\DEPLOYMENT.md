# 微信云托管部署指南

## 问题描述
二维码生成API路由配置问题已修复，需要重新部署后端服务。

## 修复内容
1. **路由配置修复**: 在 `routes/index.js` 中直接添加了 `/api/qrcode` 路由
2. **端口配置统一**: 修复了 Dockerfile 中的端口配置，统一使用 3000 端口
3. **环境变量配置**: 在 `container.config.json` 中添加了微信小程序相关环境变量

## 部署步骤

### 方法一：通过微信开发者工具部署
1. 打开微信开发者工具
2. 进入云托管控制台
3. 选择 `lieyouqi` 服务
4. 点击"版本管理"
5. 点击"新建版本"
6. 上传后端代码（backend目录）
7. 等待构建完成
8. 发布新版本

### 方法二：通过云托管控制台部署
1. 登录微信云托管控制台
2. 选择环境 `prod-5geioww562624006`
3. 选择服务 `lieyouqi`
4. 点击"版本管理" -> "新建版本"
5. 上传代码包或连接代码仓库
6. 配置构建参数
7. 等待构建和部署完成

## 环境变量配置
确保在云托管控制台中配置以下环境变量：
```
NODE_ENV=production
USE_MYSQL=true
DB_HOST=mysql
DB_PORT=3306
DB_USER=root
DB_PASSWORD={{MYSQL_PASSWORD}}
DB_NAME=lieyouqi
WX_APPID={{WX_APPID}}
WX_SECRET={{WX_SECRET}}
```

## 验证部署
部署完成后，可以通过以下方式验证：

1. **健康检查**:
   ```
   GET https://lieyouqi-158837-8-**********.sh.run.tcloudbase.com/health
   ```

2. **二维码API测试**:
   ```
   GET https://lieyouqi-158837-8-**********.sh.run.tcloudbase.com/api/qrcode?scene=test123
   ```

## 注意事项
1. 确保微信小程序的 AppID 和 Secret 已正确配置
2. 确保数据库连接正常
3. 部署后需要等待几分钟让服务完全启动
4. 如果仍有问题，检查云托管日志

## 文件变更清单
- `routes/index.js`: 添加直接二维码路由
- `container.config.json`: 添加微信环境变量
- `Dockerfile`: 修复端口配置
