const app = getApp();

Page({
  data: {
    nickname: '',
    avatarUrl: '',
    userId: '',
    qrcodeUrl: '',
    shareImage: '/images/share/share.jpg',
    generatedImage: '', // canvas合成后的图片路径
    drawing: false
  },
  onLoad() {
    // 获取用户信息，优先nickname和avatar
    const userInfo = app.globalData.userInfo || {};
    const nickname = userInfo.nickname || userInfo.nickName || '用户昵称';
    const avatarUrl = userInfo.avatar || userInfo.avatarUrl || '/images/icons2/默认头像.png';
    this.setData({
      nickname,
      avatarUrl,
      userId: userInfo._id || userInfo.id || ''
    });
    // 获取带推荐参数的小程序码
    this.generateQrcode();
  },
  generateQrcode() {
    const referrerId = this.data.userId;
    if (!referrerId) {
      wx.showToast({ title: '用户信息获取失败', icon: 'none' });
      return;
    }

    wx.showLoading({ title: '生成二维码中...', mask: true });

    // 使用云托管方式调用API
    const envId = 'prod-5geioww562624006';
    const serviceName = 'lieyouqi';

    wx.cloud.callContainer({
      config: {
        env: envId
      },
      path: '/api/system/qrcode',
      method: 'GET',
      header: {
        'X-WX-SERVICE': serviceName,
        'content-type': 'application/json'
      },
      data: { scene: referrerId },
      success: (res) => {
        wx.hideLoading();
        console.log('二维码生成响应:', res);

        if (res.statusCode === 200 && res.data && res.data.success && res.data.qrcodeUrl) {
          // 构建完整的二维码URL
          const baseUrl = 'https://lieyouqi-158837-8-1258719867.sh.run.tcloudbase.com';
          const fullQrcodeUrl = res.data.qrcodeUrl.startsWith('http')
            ? res.data.qrcodeUrl
            : baseUrl + res.data.qrcodeUrl;

          this.setData({ qrcodeUrl: fullQrcodeUrl }, () => {
            this.drawShareImage();
          });
        } else {
          console.error('二维码生成失败:', res.data);
          this.setData({ qrcodeUrl: null }); // 设置为null表示生成失败
          wx.showToast({ title: res.data?.message || '二维码生成失败', icon: 'none' });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('二维码生成请求失败:', err);
        this.setData({ qrcodeUrl: null }); // 设置为null表示生成失败
        wx.showToast({ title: '网络请求失败', icon: 'none' });
      }
    });
  },
  async drawShareImage() {
    if (this.data.drawing) return;
    this.setData({ drawing: true });

    wx.showLoading({ title: '生成分享图片...', mask: true });

    try {
      // 确保所有图片都能正确加载
      const [mainImg, avatarImg, qrcodeImg] = await Promise.all([
        this.getLocalImg(this.data.shareImage),
        this.getLocalImg(this.data.avatarUrl),
        this.getLocalImg(this.data.qrcodeUrl)
      ]);

      const ctx = wx.createCanvasContext('shareCanvas', this);

      // 设置画布背景为白色
      ctx.setFillStyle('#ffffff');
      ctx.fillRect(0, 0, 600, 800);

      // 绘制主图片（顶部背景图）
      ctx.drawImage(mainImg, 0, 0, 600, 300);

      // 绘制圆形头像
      ctx.save();
      ctx.beginPath();
      ctx.arc(70, 370, 40, 0, 2 * Math.PI);
      ctx.clip();
      ctx.drawImage(avatarImg, 30, 330, 80, 80);
      ctx.restore();

      // 绘制用户昵称
      ctx.setFontSize(24);
      ctx.setFillStyle('#333333');
      ctx.setTextAlign('left');
      const nicknameX = 130;
      const nicknameY = 385;
      ctx.fillText(this.data.nickname, nicknameX, nicknameY);

      // 绘制"向您推荐"文字
      ctx.setFontSize(20);
      ctx.setFillStyle('#888888');
      const nicknameWidth = ctx.measureText(this.data.nickname).width;
      ctx.fillText('向您推荐', nicknameX + nicknameWidth + 10, nicknameY);

      // 绘制二维码
      ctx.drawImage(qrcodeImg, 470, 330, 100, 100);

      // 绘制标语
      ctx.setFontSize(28);
      ctx.setFillStyle('#222222');
      ctx.setTextAlign('center');
      ctx.fillText('助力优质企业资产流转', 300, 480);

      // 绘制底部说明文字
      ctx.setFontSize(16);
      ctx.setFillStyle('#666666');
      ctx.fillText('扫码加入猎优企', 300, 520);

      // 完成绘制
      ctx.draw(false, () => {
        setTimeout(() => {
          wx.canvasToTempFilePath({
            canvasId: 'shareCanvas',
            width: 600,
            height: 800,
            destWidth: 600,
            destHeight: 800,
            success: (res) => {
              wx.hideLoading();
              this.setData({
                generatedImage: res.tempFilePath,
                drawing: false
              });
              console.log('分享图片生成成功:', res.tempFilePath);
            },
            fail: (err) => {
              wx.hideLoading();
              console.error('生成分享图片失败:', err);
              wx.showToast({ title: '生成图片失败', icon: 'none' });
              this.setData({ drawing: false });
            }
          }, this);
        }, 500); // 延迟500ms确保绘制完成
      });
    } catch (e) {
      wx.hideLoading();
      console.error('绘制分享图片出错:', e);
      wx.showToast({ title: '图片加载失败', icon: 'none' });
      this.setData({ drawing: false });
    }
  },
  getLocalImg(src) {
    return new Promise((resolve, reject) => {
      if (!src) return reject('无图片');
      wx.getImageInfo({
        src,
        success: res => resolve(res.path),
        fail: err => reject(err)
      });
    });
  },
  onSaveImage() {
    if (!this.data.generatedImage) {
      if (this.data.drawing) {
        wx.showToast({ title: '图片生成中，请稍候...', icon: 'loading' });
      } else {
        // 如果没有生成图片且不在绘制中，重新生成
        wx.showToast({ title: '正在重新生成图片...', icon: 'loading' });
        this.drawShareImage();
      }
      return;
    }

    // 检查相册权限
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.writePhotosAlbum'] === false) {
          // 用户之前拒绝了权限，引导用户手动开启
          wx.showModal({
            title: '需要相册权限',
            content: '保存图片需要访问您的相册，请在设置中开启权限',
            confirmText: '去设置',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.writePhotosAlbum']) {
                      this.saveImageToAlbum();
                    }
                  }
                });
              }
            }
          });
        } else {
          // 有权限或未授权过，直接保存
          this.saveImageToAlbum();
        }
      },
      fail: () => {
        // 获取设置失败，直接尝试保存
        this.saveImageToAlbum();
      }
    });
  },

  // 保存图片到相册
  saveImageToAlbum() {
    wx.showLoading({ title: '保存中...', mask: true });

    wx.saveImageToPhotosAlbum({
      filePath: this.data.generatedImage,
      success: () => {
        wx.hideLoading();
        wx.showToast({
          title: '图片已保存到相册',
          icon: 'success',
          duration: 2000
        });
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('保存图片失败:', err);

        if (err.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '保存失败',
            content: '需要您授权访问相册才能保存图片',
            confirmText: '重新授权',
            success: (res) => {
              if (res.confirm) {
                // 重新请求权限
                wx.authorize({
                  scope: 'scope.writePhotosAlbum',
                  success: () => {
                    this.saveImageToAlbum();
                  },
                  fail: () => {
                    wx.showToast({ title: '授权失败，无法保存图片', icon: 'none' });
                  }
                });
              }
            }
          });
        } else {
          wx.showToast({
            title: err.errMsg || '保存失败',
            icon: 'none'
          });
        }
      }
    });
  },
  onShareAppMessage() {
    if (!this.data.generatedImage) {
      wx.showToast({ title: '图片生成中，请稍候...', icon: 'loading' });
      return {};
    }

    // 记录分享行为
    this.recordShareAction();

    return {
      title: '向你推荐猎优企，助力优质企业资产流转',
      path: `/pages/auth/auth?scene=${this.data.userId}`,
      imageUrl: this.data.generatedImage
    };
  },

  // 记录分享行为
  recordShareAction() {
    try {
      // 可以在这里添加分享统计逻辑
      console.log('用户进行了分享操作，推荐人ID:', this.data.userId);

      // 显示分享成功提示
      wx.showToast({
        title: '分享成功',
        icon: 'success',
        duration: 1500
      });
    } catch (e) {
      console.error('记录分享行为失败:', e);
    }
  },

  // 重试生成二维码
  retryGenerateQrcode() {
    if (this.data.qrcodeUrl === null) {
      console.log('重试生成二维码');
      this.setData({ qrcodeUrl: '' }); // 重置状态
      this.generateQrcode();
    }
  }
});