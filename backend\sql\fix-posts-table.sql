-- 极简版本的posts表结构脚本
-- 这个脚本会确保posts表至少包含我们需要的6个基本字段

-- 使用数据库
USE lieyouqi;

-- 检查posts表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS posts (
  id VARCHAR(24) NOT NULL PRIMARY KEY,
  userId VARCHAR(20) NULL,
  content TEXT NULL,
  images TEXT NULL,
  userInfo TEXT NULL,
  createTime BIGINT NULL
);

-- 检查并添加必要的字段（如果不存在）
ALTER TABLE posts
ADD COLUMN IF NOT EXISTS userId VARCHAR(20) NULL,
ADD COLUMN IF NOT EXISTS content TEXT NULL,
ADD COLUMN IF NOT EXISTS images TEXT NULL,
ADD COLUMN IF NOT EXISTS userInfo TEXT NULL,
ADD COLUMN IF NOT EXISTS createTime BIGINT NULL;

-- 查看表结构
DESCRIBE posts;
