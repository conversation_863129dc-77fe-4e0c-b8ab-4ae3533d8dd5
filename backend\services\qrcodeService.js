const path = require('path');
const fs = require('fs');
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

// 你需要在config中配置小程序的appId和secret
const appId = process.env.WX_APPID;
const appSecret = process.env.WX_SECRET;
const qrcodeDir = path.join(__dirname, '../public/qrcode');
const qrcodeBaseUrl = '/public/qrcode/';

// 确保二维码目录存在
if (!fs.existsSync(qrcodeDir)) {
  fs.mkdirSync(qrcodeDir, { recursive: true });
}

async function getAccessToken() {
  const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appId}&secret=${appSecret}`;
  const res = await axios.get(url, {
    // 在云托管环境中忽略SSL证书验证
    httpsAgent: new (require('https').Agent)({
      rejectUnauthorized: false
    })
  });
  if (res.data && res.data.access_token) {
    return res.data.access_token;
  }
  console.error('获取access_token失败:', res.data);
  throw new Error('获取access_token失败:' + JSON.stringify(res.data));
}

exports.generateQrcode = async (scene) => {
  const accessToken = await getAccessToken();
  const qrcodeApi = `https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=${accessToken}`;
  const filename = `${scene}_${uuidv4()}.png`;
  const filePath = path.join(qrcodeDir, filename);
  const fileUrl = qrcodeBaseUrl + filename;

  // 生成小程序码
  const postData = {
    scene: scene,
    page: 'pages/auth/auth', // 新用户落地页
    width: 430
  };
  let response;
  try {
    response = await axios({
      url: qrcodeApi,
      method: 'POST',
      responseType: 'arraybuffer',
      data: postData,
      // 在云托管环境中忽略SSL证书验证
      httpsAgent: new (require('https').Agent)({
        rejectUnauthorized: false
      })
    });
  } catch (err) {
    console.error('请求微信小程序码API失败:', err.response ? err.response.data : err);
    throw new Error('请求微信小程序码API失败:' + (err.response ? JSON.stringify(err.response.data) : err.message));
  }
  // 检查返回内容是否为图片
  if (response.headers['content-type'] && response.headers['content-type'].includes('image')) {
    fs.writeFileSync(filePath, response.data);
    return fileUrl;
  } else {
    // 不是图片，打印详细错误
    const text = response.data.toString('utf8');
    console.error('微信小程序码API返回非图片内容:', text, '参数:', postData);
    throw new Error('微信小程序码生成失败:' + text);
  }
};