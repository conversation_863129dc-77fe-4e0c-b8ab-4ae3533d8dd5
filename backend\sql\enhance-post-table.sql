-- 增强帖子表结构，添加冗余字段以匹配前端传递的所有可能字段
-- 使用方法: 在MySQL命令行中执行 source enhance-post-table.sql

-- 选择数据库
USE lieyouqi;

-- 检查并添加 title 字段
SET @dbname = 'lieyouqi';
SET @tablename = 'posts';
SET @columnname = 'title';
SET @preparedStatement = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
   WHERE TABLE_SCHEMA = @dbname
     AND TABLE_NAME = @tablename
     AND COLUMN_NAME = @columnname) = 0,
  CONCAT("ALTER TABLE ", @tablename, " ADD COLUMN ", @columnname, " VARCHAR(255);"),
  "SELECT 'Column already exists';"
));
PREPARE alterIfNotExists FROM @preparedStatement;
EXECUTE alterIfNotExists;
DEALLOCATE PREPARE alterIfNotExists;

-- 检查并添加 isLiked 字段
SET @columnname = 'isLiked';
SET @preparedStatement = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
   WHERE TABLE_SCHEMA = @dbname

-- 查看表结构
DESCRIBE posts;

-- 检查并添加 visible 字段
SET @columnname = 'visible';
SET @preparedStatement = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
   WHERE TABLE_SCHEMA = 'lieyouqi'
     AND TABLE_NAME = 'posts'
     AND TABLE_NAME = @tablename
     AND COLUMN_NAME = @columnname) = 0,
  CONCAT("ALTER TABLE ", @tablename, " ADD COLUMN ", @columnname, " BOOLEAN DEFAULT true;"),
  "SELECT 'Column already exists';"
));
PREPARE alterIfNotExists FROM @preparedStatement;
EXECUTE alterIfNotExists;
DEALLOCATE PREPARE alterIfNotExists;

-- 检查并添加 isHidden 字段
SET @columnname = 'isHidden';
SET @preparedStatement = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
   WHERE TABLE_SCHEMA = @dbname
     AND TABLE_NAME = @tablename
     AND COLUMN_NAME = @columnname) = 0,
  CONCAT("ALTER TABLE ", @tablename, " ADD COLUMN ", @columnname, " BOOLEAN DEFAULT false;"),
  "SELECT 'Column already exists';"
));
PREPARE alterIfNotExists FROM @preparedStatement;
EXECUTE alterIfNotExists;
DEALLOCATE PREPARE alterIfNotExists;

-- 显示表结构以确认修改
DESCRIBE posts;
