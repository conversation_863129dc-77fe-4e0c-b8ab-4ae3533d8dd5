<!--pages/profile/profile.wxml-->
<view class="profile-container">
  <!-- 顶部个人信息 -->
  <view class="profile-header">
    <view class="user-info-row" wx:if="{{isLogin}}">
      <image class="avatar-large" src="{{userInfo.avatar || userInfo.avatarUrl || '../../images/icons2/男头像.png'}}"></image>
      <view class="user-info-content">
        <view class="user-name">{{userInfo.nickname || userInfo.nickName || '用户'}}</view>
        <view class="user-id">ID号：{{userInfo.id || userInfo._id || ''}}</view>
      </view>
      <view class="vip-badge">
        <text>年度会员</text>
        <image class="vip-icon" src="../../images/icons/vip.png"></image>
      </view>
    </view>

    <view class="login-prompt" wx:else>
      <image class="avatar-large" src="../../images/icons2/男头像.png"></image>
      <view class="login-text">登录后查看个人信息</view>
      <view class="login-btn" bindtap="goToLogin">立即登录</view>
    </view>

    <!-- 数据统计 -->
    <view class="stats-bar">
      <view class="stats-item" bindtap="viewFollowing">
        <view class="stats-num">{{isLogin ? (stats.followCount || 0) : 0}}</view>
        <view class="stats-label">关注</view>
      </view>
      <view class="stats-item" bindtap="viewFollowers">
        <view class="stats-num">{{isLogin ? (stats.followerCount || 0) : 0}}</view>
        <view class="stats-label">粉丝</view>
      </view>
      <view class="stats-item" bindtap="viewLikes">
        <view class="stats-num">{{isLogin ? (stats.likeCount || 0) : 0}}</view>
        <view class="stats-label">获赞</view>
      </view>
      <view class="stats-item" bindtap="viewPoints">
        <view class="stats-num">{{isLogin ? (stats.points || 0) : 0}}</view>
        <view class="stats-label">积分</view>
      </view>
      <view class="stats-item" bindtap="viewBalance">
        <view class="stats-num">{{isLogin ? (stats.balance || 0) : 0}}</view>
        <view class="stats-label">余额</view>
      </view>
    </view>

    <!-- 分隔线 -->
    <view class="stats-divider"></view>

    <!-- 内容统计 -->
    <view class="content-stats">
      <view class="content-stats-item" bindtap="viewMyPublish">
        <view class="content-stats-num">{{isLogin ? (stats.postCount || 0) : 0}}</view>
        <view class="content-stats-label">我的发布</view>
      </view>
      <view class="content-stats-item" bindtap="viewMyComments">
        <view class="content-stats-num">{{isLogin ? (stats.commentCount || 0) : 0}}</view>
        <view class="content-stats-label">我的评论</view>
      </view>
      <view class="content-stats-item" bindtap="viewMyPromotions">
        <view class="content-stats-num">{{isLogin ? (stats.promotionCount || 0) : 0}}</view>
        <view class="content-stats-label">我的推广</view>
      </view>
    </view>
  </view>

  <!-- 轮播图 -->
  <view class="banner-section">
    <swiper class="banner-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}" circular="{{true}}" indicator-color="rgba(255, 255, 255, 0.6)" indicator-active-color="#1E6A9E">
      <block wx:if="{{banners && banners.length > 0}}">
        <swiper-item wx:for="{{banners}}" wx:key="id" bindtap="onBannerTap" data-id="{{item.id}}" data-url="{{item.linkUrl}}">
          <image class="banner-image" src="{{item.imageUrl}}" mode="aspectFill" lazy-load="{{false}}"></image>
          <view class="banner-title" wx:if="{{item.title}}">{{item.title}}</view>
        </swiper-item>
      </block>
      <block wx:else>
        <!-- 默认轮播图内容 -->
        <swiper-item>
          <image class="banner-image" src="/images/lunbo/001.jpeg" mode="aspectFill"></image>
          <view class="banner-title">企业服务专场</view>
        </swiper-item>
        <swiper-item>
          <image class="banner-image" src="/images/lunbo/002.jpg" mode="aspectFill"></image>
          <view class="banner-title">知识产权保护与商标注册</view>
        </swiper-item>
        <swiper-item>
          <image class="banner-image" src="/images/lunbo/003.png" mode="aspectFill"></image>
          <view class="banner-title">企业并购与资产重组</view>
        </swiper-item>
      </block>
    </swiper>
  </view>

  <!-- 订单管理 -->
  <view class="content-card">
    <view class="order-header">
      <view class="order-title">我的订单</view>
      <view class="view-all" bindtap="viewAllOrders">全部 》</view>
    </view>

    <view class="order-grid">
      <view class="order-item" bindtap="viewUnpaidOrders">
        <image class="order-icon" src="/images/icons2/待付款.png"></image>
        <view class="order-name">待付款</view>
        <view class="order-badge" wx:if="{{stats.unpaidCount > 0}}">{{stats.unpaidCount}}</view>
      </view>
      <view class="order-item" bindtap="viewUnshippedOrders">
        <image class="order-icon" src="/images/icons2/待发货.png"></image>
        <view class="order-name">待发货</view>
        <view class="order-badge" wx:if="{{stats.unshippedCount > 0}}">{{stats.unshippedCount}}</view>
      </view>
      <view class="order-item" bindtap="viewUnreceivedOrders">
        <image class="order-icon" src="/images/icons2/待收货.png"></image>
        <view class="order-name">待收货</view>
        <view class="order-badge" wx:if="{{stats.unreceivedCount > 0}}">{{stats.unreceivedCount}}</view>
      </view>
      <view class="order-item" bindtap="viewUnratedOrders">
        <image class="order-icon" src="/images/icons2/退换货.png"></image>
        <view class="order-name">退换货</view>
        <view class="order-badge" wx:if="{{stats.unratedCount > 0}}">{{stats.unratedCount}}</view>
      </view>
    </view>

  </view>

  <!-- 分隔线 -->
  <view class="divider"></view>

  <!-- 其他功能 -->
  <view class="other-functions">
    <view class="other-title">其他功能</view>

    <view class="menu-item" bindtap="goToSettings">
      <image class="menu-icon" src="/images/icons2/我的.png"></image>
      <view class="menu-name">账号设置</view>
      <view class="arrow-right">》</view>
    </view>

    <view class="menu-item" bindtap="viewAddress">
      <image class="menu-icon" src="/images/icons2/地址.png"></image>
      <view class="menu-name">收货地址</view>
      <view class="arrow-right">》</view>
    </view>

    <view class="menu-item" bindtap="contactService">
      <image class="menu-icon" src="/images/icons2/客服.png"></image>
      <view class="menu-name">客服中心</view>
      <view class="arrow-right">》</view>
    </view>

    <view class="menu-item" bindtap="goToAbout">
      <image class="menu-icon" src="/images/icons2/关于.png"></image>
      <view class="menu-name">关于我们</view>
      <view class="arrow-right">》</view>
    </view>

    <!-- 退出登录按钮，仅在登录状态下显示 -->
    <view class="menu-item logout-item" bindtap="logout" wx:if="{{isLogin}}">
      <image class="menu-icon" src="/images/icons2/退出.png"></image>
      <view class="menu-name logout-text">退出登录</view>
      <view class="arrow-right">》</view>
    </view>
  </view>

  <!-- 开发者选项已移除 -->

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>

  <!-- 开发者选项触发区域已移除 -->
</view>
