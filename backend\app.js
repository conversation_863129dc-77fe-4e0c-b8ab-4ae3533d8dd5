require('dotenv').config();
/**
 * 应用入口
 */
const express = require('express');
const cors = require('cors');
const config = require('./config');
const configMiddleware = require('./config/middleware');
const routes = require('./routes');
const errorMiddleware = require('./middleware/error');
const db = require('./config/db');
const path = require('path');
const uploadRoutes = require('./routes/upload');

// 创建Express应用
const app = express();

// 启用 CORS
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));

// 解析 JSON 请求体
app.use(express.json());

// 静态文件服务
app.use('/uploads', express.static(path.resolve(__dirname, '../uploads'), {
  setHeaders: (res) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
    res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
  }
}));
app.use('/__tmp__', express.static(path.resolve(__dirname, '../__tmp__'), {
  setHeaders: (res) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
    res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
  }
}));
app.use('/tmp', express.static(path.resolve(__dirname, '../__tmp__'), {
  setHeaders: (res) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
    res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
  }
}));
app.use(express.static('public', {
  setHeaders: (res) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
  }
}));
app.use('/images', express.static(path.resolve(__dirname, '../images'), {
  setHeaders: (res) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
  },
  index: false,
  redirect: false,
  dotfiles: 'allow'
}));
app.use('/public/qrcode', require('express').static(__dirname + '/public/qrcode'));

// 配置中间件
configMiddleware(app);

// API路由
app.use(config.apiPrefix, routes);
// 直接挂载/upload，兼容小程序直传
app.use('/upload', uploadRoutes);

// 健康检查路由 - 添加两个路径以确保兼容性
app.get(`${config.apiPrefix}/health`, (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString()
  });
});

// 额外的健康检查路由，不依赖于apiPrefix
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString()
  });
});

// 首页路由
app.get('/', (req, res) => {
  res.json({
    message: '猎优企小程序API服务',
    version: '1.0.0',
    status: 'running'
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: err.message
  });
});

module.exports = app;
