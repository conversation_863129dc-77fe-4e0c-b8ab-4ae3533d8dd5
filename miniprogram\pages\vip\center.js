const { validateLoginState } = require('../../utils/login-state-manager');

Page({
  data: {
    userInfo: {},
    vipInfo: {
      expireDate: '2026年5月12日'
    },
    banners: [
      '/images/lunbo/001.jpeg',
      '/images/lunbo/002.jpg',
      '/images/lunbo/003.png',
      '/images/lunbo/004.jpg',
      '/images/lunbo/004.webp'
    ],
    vipProducts: [
      {
        id: 1,
        name: '年度会员',
        benefits: ['专属标识', '专属客服', '专属活动', '更多权益...'],
        buttonText: '会员续费'
      },
      {
        id: 2,
        name: '超级会员',
        benefits: ['全部年度会员权益', '专属折扣', '专属礼包', '专属活动', '更多超级权益...'],
        buttonText: '立即开通'
      }
    ]
  },

  onLoad: function (options) {
    this.checkLoginAndInit();
  },

  checkLoginAndInit() {
    const that = this;
    validateLoginState().then(res => {
      if (!res.isValid) {
        wx.showModal({
          title: '请先登录',
          content: '登录后可查看会员中心',
          showCancel: false,
          success: () => {
            wx.navigateTo({ url: '/pages/auth/auth' });
          }
        });
        return;
      }
      // 已登录，直接用本地全局信息，兼容多字段
      const app = getApp();
      let userInfo = (app.globalData && app.globalData.userInfo) || {};
      // 字段兼容
      userInfo.avatarUrl = userInfo.avatarUrl || userInfo.avatar || '/images/default-avatar.png';
      userInfo.nickName = userInfo.nickName || userInfo.nickname || userInfo.username || '昵称';
      userInfo.id = userInfo.id || userInfo._id || userInfo.userid || 'XXXXXXXXXX';
      userInfo.vipLevel = userInfo.vipLevel || userInfo.level || userInfo.vip || '年度会员';
      that.setData({ userInfo });
    });
  }
}); 