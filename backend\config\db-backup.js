const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
const config = require('./config');

async function backupDatabase() {
  try {
    const connection = await mysql.createConnection({
      host: config.db.host,
      user: config.db.user,
      password: config.db.password,
      database: config.db.database
    });

    // 创建备份目录
    const backupDir = path.join(__dirname, '../../backups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir);
    }

    // 获取所有表
    const [tables] = await connection.query('SHOW TABLES');
    const tableNames = tables.map(table => Object.values(table)[0]);

    // 备份每个表
    for (const tableName of tableNames) {
      const [rows] = await connection.query(`SELECT * FROM ${tableName}`);
      const backupFile = path.join(backupDir, `${tableName}_${Date.now()}.json`);
      fs.writeFileSync(backupFile, JSON.stringify(rows, null, 2));
      console.log(`表 ${tableName} 备份完成: ${backupFile}`);
    }

    await connection.end();
    console.log('数据库备份完成');
    return true;
  } catch (error) {
    console.error('数据库备份失败:', error);
    throw error;
  }
}

module.exports = {
  backupDatabase
}; 