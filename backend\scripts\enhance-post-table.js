/**
 * 增强帖子表结构，添加冗余字段以匹配前端传递的所有可能字段
 */
const mysql = require('mysql2/promise');
require('dotenv').config();

// 微信云托管环境变量优先
const dbHost = process.env.MYSQL_IP || process.env.DB_HOST || 'localhost';
const dbPort = process.env.MYSQL_PORT || 3306;
const dbUser = process.env.MYSQL_USERNAME || process.env.DB_USER || 'root';
const dbName = process.env.MYSQL_DATABASE || process.env.DB_NAME || 'lieyouqi';
const dbPassword = (process.env.MYSQL_PASSWORD || process.env.DB_PASSWORD || '').replace(/"/g, '');

async function enhancePostTable() {
  let connection;
  try {
    console.log('开始增强帖子表结构...');
    
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: dbHost,
      port: dbPort,
      user: dbUser,
      password: dbPassword,
      database: dbName
    });
    
    console.log('数据库连接成功');
    
    // 检查并添加 title 字段
    await addColumnIfNotExists(connection, 'posts', 'title', 'VARCHAR(255)');
    
    // 检查并添加 isLiked 字段
    await addColumnIfNotExists(connection, 'posts', 'isLiked', 'BOOLEAN DEFAULT false');
    
    // 检查并添加 product 字段（作为 linkedProducts 的别名）
    await addColumnIfNotExists(connection, 'posts', 'product', 'TEXT');
    
    // 检查并添加 topics 字段（作为 topic 的复数形式）
    await addColumnIfNotExists(connection, 'posts', 'topics', 'TEXT');
    
    // 检查并添加 visible 字段（作为 isPublic 的别名）
    await addColumnIfNotExists(connection, 'posts', 'visible', 'BOOLEAN DEFAULT true');
    
    // 检查并添加 isHidden 字段（作为 visible 的反义词）
    await addColumnIfNotExists(connection, 'posts', 'isHidden', 'BOOLEAN DEFAULT false');
    
    console.log('帖子表结构增强完成！');
  } catch (error) {
    console.error('增强帖子表结构失败:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 辅助函数：如果列不存在则添加列
async function addColumnIfNotExists(connection, table, column, definition) {
  try {
    // 检查列是否存在
    const [columns] = await connection.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? AND COLUMN_NAME = ?
    `, [dbName, table, column]);
    
    if (columns.length === 0) {
      // 列不存在，添加列
      console.log(`添加 ${table}.${column} 字段...`);
      await connection.query(`ALTER TABLE ${table} ADD COLUMN ${column} ${definition}`);
      console.log(`${table}.${column} 字段添加成功`);
    } else {
      console.log(`${table}.${column} 字段已存在，跳过`);
    }
  } catch (error) {
    console.error(`添加 ${table}.${column} 字段失败:`, error);
    throw error;
  }
}

// 执行增强操作
enhancePostTable()
  .then(() => {
    console.log('脚本执行完成');
    process.exit(0);
  })
  .catch(err => {
    console.error('脚本执行失败:', err);
    process.exit(1);
  });
