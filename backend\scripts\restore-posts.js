/**
 * 从posts_backup恢复posts表的脚本
 */
require('dotenv').config();
const mysql = require('mysql2/promise');
const config = require('../config/config');

// 数据库配置
const dbConfig = {
  host: config.db.host,
  port: config.db.port,
  user: config.db.user,
  password: config.db.password,
  database: config.db.database,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

console.log('数据库配置:', {
  host: dbConfig.host,
  port: dbConfig.port,
  user: dbConfig.user,
  database: dbConfig.database,
  passwordLength: dbConfig.password ? dbConfig.password.length : 0
});

async function main() {
  let connection;
  try {
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功');

    // 检查posts_backup表是否存在
    const [backupTables] = await connection.query(
      "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = 'posts_backup'",
      [dbConfig.database]
    );
    
    const backupExists = backupTables[0].count > 0;
    console.log(`备份表posts_backup ${backupExists ? '存在' : '不存在'}`);

    if (!backupExists) {
      console.error('备份表posts_backup不存在，无法恢复数据');
      return;
    }

    // 检查posts_backup表结构
    const [backupColumns] = await connection.query('DESCRIBE posts_backup');
    console.log('备份表posts_backup的结构:');
    backupColumns.forEach(col => {
      console.log(`- ${col.Field} (${col.Type})`);
    });

    // 检查posts表是否存在
    const [postsTables] = await connection.query(
      "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = 'posts'",
      [dbConfig.database]
    );
    
    const postsExists = postsTables[0].count > 0;
    console.log(`posts表 ${postsExists ? '存在' : '不存在'}`);

    if (postsExists) {
      // 备份当前的posts表
      console.log('备份当前的posts表到posts_current...');
      await connection.query('CREATE TABLE IF NOT EXISTS posts_current AS SELECT * FROM posts');
      console.log('当前posts表已备份到posts_current');

      // 删除当前的posts表
      console.log('删除当前的posts表...');
      await connection.query('DROP TABLE posts');
      console.log('当前posts表已删除');
    }

    // 创建新的posts表，使用与posts_backup相同的结构
    console.log('创建新的posts表，使用与posts_backup相同的结构...');
    await connection.query(`
      CREATE TABLE posts (
        id VARCHAR(24) NOT NULL PRIMARY KEY,
        userId VARCHAR(20) NULL,
        content TEXT NULL,
        images TEXT NULL,
        userInfo TEXT NULL,
        createTime BIGINT NULL,
        likeCount INT DEFAULT 0,
        commentCount INT DEFAULT 0,
        shareCount INT DEFAULT 0,
        video VARCHAR(255) NULL,
        topic VARCHAR(100) NULL,
        region VARCHAR(255) NULL,
        isPublic BOOLEAN DEFAULT true,
        allowComment BOOLEAN DEFAULT true,
        allowForward BOOLEAN DEFAULT true,
        linkedProducts TEXT NULL,
        location VARCHAR(255) NULL,
        title VARCHAR(255) NULL,
        updateTime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        extra1 TEXT NULL,
        extra2 TEXT NULL,
        extra3 TEXT NULL,
        extra4 TEXT NULL,
        extra5 TEXT NULL,
        isLiked BOOLEAN DEFAULT false,
        product TEXT NULL,
        topics TEXT NULL,
        visible BOOLEAN DEFAULT true,
        isHidden BOOLEAN DEFAULT false
      )
    `);
    console.log('新的posts表已创建');

    // 从备份表恢复数据
    console.log('从备份表恢复数据...');
    await connection.query(`
      INSERT INTO posts 
      SELECT * FROM posts_backup
    `);
    
    // 检查恢复的数据
    const [restoredCount] = await connection.query('SELECT COUNT(*) as count FROM posts');
    console.log(`已恢复 ${restoredCount[0].count} 条数据到posts表`);

    // 确保所有帖子都有主题
    console.log('确保所有帖子都有主题...');
    await connection.query("UPDATE posts SET topic = '企业服务' WHERE topic IS NULL OR topic = ''");
    
    const [noTopicCount] = await connection.query("SELECT COUNT(*) as count FROM posts WHERE topic IS NULL OR topic = ''");
    console.log(`还有 ${noTopicCount[0].count} 条帖子没有主题`);

    console.log('数据恢复完成');
  } catch (error) {
    console.error('发生错误:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

main();
