require('dotenv').config();
const mysql = require('mysql2/promise');

// 微信云托管环境变量优先
const dbConfig = {
  host: process.env.MYSQL_IP || process.env.DB_HOST || 'localhost',
  port: process.env.MYSQL_PORT || 3306,
  user: process.env.MYSQL_USERNAME || process.env.DB_USER || 'root',
  database: process.env.MYSQL_DATABASE || process.env.DB_NAME || 'lieyouqi',
  password: (process.env.MYSQL_PASSWORD || process.env.DB_PASSWORD || '').replace(/"/g, '')
};

console.log('数据库配置:');
console.log('- 主机:', dbConfig.host);
console.log('- 端口:', dbConfig.port);
console.log('- 用户:', dbConfig.user);
console.log('- 数据库:', dbConfig.database);
console.log('- 密码长度:', dbConfig.password.length);

/**
 * 添加群公告字段到groups表
 */
async function addGroupAnnouncementField() {
  let connection;

  try {
    console.log('开始添加群公告字段...');

    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);

    // 检查字段是否已存在
    console.log('检查announcement字段是否已存在...');
    const [columns] = await connection.query(`
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'groups' AND COLUMN_NAME = 'announcement'
    `, [dbConfig.database]);

    if (columns.length > 0) {
      console.log('announcement字段已存在，无需添加');
      return;
    }

    // 添加announcement字段
    console.log('添加announcement字段...');
    await connection.query(`
      ALTER TABLE \`groups\`
      ADD COLUMN \`announcement\` TEXT COMMENT '群公告'
      AFTER \`description\`
    `);

    console.log('群公告字段添加成功！');

    // 为现有群组设置默认公告
    console.log('为现有群组设置默认公告...');
    await connection.query(`
      UPDATE \`groups\`
      SET \`announcement\` = '欢迎加入本群！'
      WHERE \`announcement\` IS NULL OR \`announcement\` = ''
    `);

    console.log('默认公告设置完成！');

  } catch (error) {
    console.error('添加群公告字段失败:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  addGroupAnnouncementField()
    .then(() => {
      console.log('群公告字段添加完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('操作失败:', error);
      process.exit(1);
    });
}

module.exports = {
  addGroupAnnouncementField
};
