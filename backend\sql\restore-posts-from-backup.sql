-- 使用数据库
USE lieyouqi;

-- 检查posts_backup表是否存在
SELECT COUNT(*) INTO @backup_exists FROM information_schema.tables 
WHERE table_schema = 'lieyouqi' AND table_name = 'posts_backup';

-- 如果备份表存在，则从备份表恢复
SET @sql = IF(@backup_exists > 0, 
  'DROP TABLE IF EXISTS posts;
   CREATE TABLE posts (
     id VARCHAR(24) NOT NULL PRIMARY KEY,
     userId VARCHAR(20) NULL,
     content TEXT NULL,
     images TEXT NULL,
     userInfo TEXT NULL,
     createTime BIGINT NULL,
     likeCount INT DEFAULT 0,
     commentCount INT DEFAULT 0,
     shareCount INT DEFAULT 0,
     video VARCHAR(255) NULL,
     topic VARCHAR(100) NULL,
     region VARCHAR(255) NULL,
     isPublic BOOLEAN DEFAULT true,
     allowComment BOOLEAN DEFAULT true,
     allowForward BOOLEAN DEFAULT true,
     linkedProducts TEXT NULL
   );
   
   INSERT INTO posts (id, userId, content, images, userInfo, createTime, likeCount, commentCount, shareCount, video, topic, region, isPublic, allowComment, allowForward, linkedProducts)
   SELECT 
     id, 
     userId, 
     content, 
     images, 
     userInfo, 
     createTime, 
     IFNULL(likeCount, 0), 
     IFNULL(commentCount, 0), 
     IFNULL(shareCount, 0), 
     video, 
     topic,
     region,
     IFNULL(isPublic, true),
     IFNULL(allowComment, true),
     IFNULL(allowForward, true),
     linkedProducts
   FROM posts_backup;',
   
  'SELECT "备份表 posts_backup 不存在，无法恢复数据" AS message;'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 查看表结构
DESCRIBE posts;

-- 查看恢复的数据条数
SELECT COUNT(*) AS total_posts FROM posts;
