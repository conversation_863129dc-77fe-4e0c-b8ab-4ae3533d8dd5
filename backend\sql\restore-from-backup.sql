-- 使用数据库
USE lieyouqi;

-- 检查posts_backup表是否存在
SELECT COUNT(*) INTO @backup_exists FROM information_schema.tables 
WHERE table_schema = 'lieyouqi' AND table_name = 'posts_backup';

SELECT CONCAT('备份表posts_backup ', IF(@backup_exists > 0, '存在', '不存在')) AS message;

-- 如果备份表存在，则从备份表恢复
SET @sql = IF(@backup_exists > 0, 
  'CREATE TABLE IF NOT EXISTS posts_current AS SELECT * FROM posts;
   DROP TABLE IF EXISTS posts;
   
   CREATE TABLE posts (
     id VARCHAR(24) NOT NULL PRIMARY KEY,
     userId VARCHAR(20) NULL,
     content TEXT NULL,
     images TEXT NULL,
     userInfo TEXT NULL,
     createTime BIGINT NULL,
     likeCount INT DEFAULT 0,
     commentCount INT DEFAULT 0,
     shareCount INT DEFAULT 0,
     video VARCHAR(255) NULL,
     topic VARCHAR(100) NULL,
     region VARCHAR(255) NULL,
     isPublic BOOLEAN DEFAULT true,
     allowComment BOOLEAN DEFAULT true,
     allowForward BOOLEAN DEFAULT true,
     linkedProducts TEXT NULL,
     location VARCHAR(255) NULL,
     title VARCHAR(255) NULL,
     updateTime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
     extra1 TEXT NULL,
     extra2 TEXT NULL,
     extra3 TEXT NULL,
     extra4 TEXT NULL,
     extra5 TEXT NULL,
     isLiked BOOLEAN DEFAULT false,
     product TEXT NULL,
     topics TEXT NULL,
     visible BOOLEAN DEFAULT true,
     isHidden BOOLEAN DEFAULT false
   );
   
   INSERT INTO posts SELECT * FROM posts_backup;
   
   UPDATE posts SET topic = "企业服务" WHERE topic IS NULL OR topic = "";
   
   SELECT COUNT(*) AS restored_count FROM posts;',
   
  'SELECT "备份表 posts_backup 不存在，无法恢复数据" AS message;'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 查看表结构
DESCRIBE posts;

-- 查看恢复的数据条数
SELECT COUNT(*) AS total_posts FROM posts;
