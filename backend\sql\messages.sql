CREATE TABLE IF NOT EXISTS messages (
  id INT AUTO_INCREMENT PRIMARY KEY,
  senderId VARCHAR(32) NOT NULL COMMENT '发送者ID',
  receiverId VARCHAR(32) NOT NULL COMMENT '接收者ID',
  content TEXT NOT NULL COMMENT '消息内容',
  type VARCHAR(20) NOT NULL DEFAULT 'text' COMMENT '消息类型：text-文本，image-图片',
  isRead BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已读',
  createTime BIGINT NOT NULL COMMENT '创建时间',
  INDEX idx_sender (senderId),
  INDEX idx_receiver (receiverId),
  INDEX idx_create_time (createTime)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='私信消息表'; 