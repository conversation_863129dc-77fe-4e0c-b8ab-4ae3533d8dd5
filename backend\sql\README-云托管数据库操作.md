# 微信云托管数据库操作指南

## 添加群公告字段

### 方法一：在微信云托管控制台执行

1. 登录微信云托管控制台
2. 进入"数据库"选项卡
3. 选择您的MySQL实例
4. 点击"数据库管理"或"phpMyAdmin"
5. 选择数据库（通常是`lieyouqi`）
6. 点击"SQL"选项卡
7. 复制并执行以下SQL语句：

#### 推荐使用（智能检查版本）：
```sql
-- 复制 add-group-announcement-field.sql 中的内容
```

#### 简单版本（如果上面的不支持）：
```sql
-- 复制 add-group-announcement-simple.sql 中的内容
```

### 方法二：通过云托管终端（如果可用）

1. 在云托管控制台找到您的服务
2. 点击"终端"或"调试"
3. 在终端中执行：
```bash
cd /app
npm run add-group-announcement
```

### 验证字段添加成功

执行以下SQL查看表结构：
```sql
DESCRIBE `groups`;
```

应该能看到`announcement`字段，类型为`TEXT`。

### 注意事项

1. 如果字段已存在，简单版本会报错，但不会影响数据库
2. 智能检查版本会自动跳过已存在的字段
3. 执行后所有现有群组都会有默认公告："欢迎加入本群！"
4. 新创建的群组也会自动设置默认公告

### 常见问题

**Q: 执行SQL时报错"字段已存在"**
A: 这是正常的，说明字段已经添加过了，可以忽略这个错误。

**Q: 如何确认字段添加成功？**
A: 执行`DESCRIBE groups;`查看表结构，或者查看现有群组数据。

**Q: 前端显示不了群公告**
A: 确保后端代码已更新，Group模型中包含了announcement字段的处理。
