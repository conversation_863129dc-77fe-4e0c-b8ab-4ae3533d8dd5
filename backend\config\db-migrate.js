const mysql = require('mysql2/promise');
const config = require('./config');

async function migrateDatabase() {
  try {
    const connection = await mysql.createConnection({
      host: config.db.host,
      user: config.db.user,
      password: config.db.password,
      database: config.db.database
    });

    // 检查表是否存在
    const [tables] = await connection.query('SHOW TABLES');
    const tableNames = tables.map(table => Object.values(table)[0]);

    // 如果表不存在，创建表
    if (!tableNames.includes('users')) {
      await connection.query(`
        CREATE TABLE users (
          id VARCHAR(20) PRIMARY KEY,
          username VARCHAR(50) NOT NULL,
          password VARCHAR(100) NOT NULL,
          nickname VARCHAR(50),
          avatar VARCHAR(255),
          gender ENUM('male', 'female', 'other'),
          phone VARCHAR(20),
          email VARCHAR(100),
          createTime BIGINT,
          updateTime BIGINT
        )
      `);
    }

    if (!tableNames.includes('posts')) {
      await connection.query(`
        CREATE TABLE posts (
          id VARCHAR(24) PRIMARY KEY,
          userId VARCHAR(20) NOT NULL,
          content TEXT,
          images TEXT,
          video VARCHAR(255),
          topic VARCHAR(100),
          location VARCHAR(255),
          isPublic BOOLEAN DEFAULT true,
          allowComment BOOLEAN DEFAULT true,
          allowForward BOOLEAN DEFAULT true,
          linkedProducts TEXT,
          region VARCHAR(100),
          userInfo TEXT,
          createTime BIGINT,
          likeCount INT DEFAULT 0,
          commentCount INT DEFAULT 0,
          shareCount INT DEFAULT 0,
          FOREIGN KEY (userId) REFERENCES users(id)
        )
      `);
    }

    if (!tableNames.includes('comments')) {
      await connection.query(`
        CREATE TABLE comments (
          id VARCHAR(24) PRIMARY KEY,
          postId VARCHAR(24) NOT NULL,
          userId VARCHAR(20) NOT NULL,
          content TEXT NOT NULL,
          createTime BIGINT,
          FOREIGN KEY (postId) REFERENCES posts(id),
          FOREIGN KEY (userId) REFERENCES users(id)
        )
      `);
    }

    if (!tableNames.includes('likes')) {
      await connection.query(`
        CREATE TABLE likes (
          id VARCHAR(24) PRIMARY KEY,
          postId VARCHAR(24) NOT NULL,
          userId VARCHAR(20) NOT NULL,
          createTime BIGINT,
          FOREIGN KEY (postId) REFERENCES posts(id),
          FOREIGN KEY (userId) REFERENCES users(id)
        )
      `);
    }

    // 如果users表已存在但没有referrerId字段，自动添加
    if (tableNames.includes('users')) {
      const [columns] = await connection.query('SHOW COLUMNS FROM users');
      const hasReferrer = columns.some(col => col.Field === 'referrerId');
      if (!hasReferrer) {
        await connection.query('ALTER TABLE users ADD COLUMN referrerId VARCHAR(20)');
      }
    }

    // 创建公司信息表（如果不存在）
    if (!tableNames.includes('company_info')) {
      await connection.query(`
        CREATE TABLE company_info (
          id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
          company_name VARCHAR(100) NOT NULL COMMENT '公司名称',
          company_description TEXT COMMENT '公司简介',
          company_address VARCHAR(255) COMMENT '公司地址',
          company_phone VARCHAR(50) COMMENT '公司电话',
          company_email VARCHAR(100) COMMENT '公司邮箱',
          company_website VARCHAR(255) COMMENT '公司网站',
          business_hours VARCHAR(100) COMMENT '营业时间',
          company_logo VARCHAR(255) COMMENT '公司Logo',
          contact_person VARCHAR(50) COMMENT '联系人',
          fax VARCHAR(50) COMMENT '传真号码',
          postal_code VARCHAR(20) COMMENT '邮政编码',
          company_type VARCHAR(50) COMMENT '公司类型',
          established_date DATE COMMENT '成立日期',
          registration_number VARCHAR(100) COMMENT '注册号码',
          social_credit_code VARCHAR(100) COMMENT '统一社会信用代码',
          legal_representative VARCHAR(50) COMMENT '法定代表人',
          registered_capital DECIMAL(15,2) COMMENT '注册资本',
          business_scope TEXT COMMENT '经营范围',
          company_status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '公司状态',
          createTime BIGINT NOT NULL COMMENT '创建时间',
          updateTime BIGINT NOT NULL COMMENT '更新时间',
          INDEX idx_company_name (company_name),
          INDEX idx_create_time (createTime),
          INDEX idx_update_time (updateTime)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司信息表'
      `);
      console.log('公司信息表创建成功');
    }

    // 创建用户留言表（如果不存在）
    if (!tableNames.includes('user_feedback')) {
      await connection.query(`
        CREATE TABLE user_feedback (
          id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
          user_id VARCHAR(20) COMMENT '用户ID（可为空，支持未登录用户留言）',
          name VARCHAR(50) NOT NULL COMMENT '留言人姓名',
          contact VARCHAR(20) NOT NULL COMMENT '联系方式（手机号）',
          message TEXT NOT NULL COMMENT '留言内容',
          status ENUM('pending', 'processing', 'resolved') DEFAULT 'pending' COMMENT '处理状态',
          admin_reply TEXT COMMENT '管理员回复',
          create_time BIGINT NOT NULL COMMENT '创建时间',
          update_time BIGINT NOT NULL COMMENT '更新时间',
          INDEX idx_user_id (user_id),
          INDEX idx_status (status),
          INDEX idx_create_time (create_time),
          INDEX idx_contact (contact)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户留言表'
      `);
      console.log('用户留言表创建成功');
    }

    await connection.end();
    console.log('数据库迁移完成');
    return true;
  } catch (error) {
    console.error('数据库迁移失败:', error);
    throw error;
  }
}

module.exports = {
  migrateDatabase
};