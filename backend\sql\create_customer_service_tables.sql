-- 客服中心相关表创建脚本
-- 包括公司信息表和用户留言表

USE lieyouqi;

-- 1. 创建公司信息表
CREATE TABLE IF NOT EXISTS company_info (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  company_name VARCHAR(100) NOT NULL COMMENT '公司名称',
  company_description TEXT COMMENT '公司简介',
  company_address VARCHAR(255) COMMENT '公司地址',
  company_phone VARCHAR(50) COMMENT '公司电话',
  company_email VARCHAR(100) COMMENT '公司邮箱',
  company_website VARCHAR(255) COMMENT '公司网站',
  business_hours VARCHAR(100) COMMENT '营业时间',
  company_logo VARCHAR(255) COMMENT '公司Logo',
  contact_person VARCHAR(50) COMMENT '联系人',
  fax VARCHAR(50) COMMENT '传真号码',
  postal_code VARCHAR(20) COMMENT '邮政编码',
  company_type VARCHAR(50) COMMENT '公司类型',
  established_date DATE COMMENT '成立日期',
  registration_number VARCHAR(100) COMMENT '注册号码',
  social_credit_code VARCHAR(100) COMMENT '统一社会信用代码',
  legal_representative VARCHAR(50) COMMENT '法定代表人',
  registered_capital DECIMAL(15,2) COMMENT '注册资本',
  business_scope TEXT COMMENT '经营范围',
  company_status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '公司状态',
  createTime BIGINT NOT NULL COMMENT '创建时间',
  updateTime BIGINT NOT NULL COMMENT '更新时间',
  INDEX idx_company_name (company_name),
  INDEX idx_create_time (createTime),
  INDEX idx_update_time (updateTime)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司信息表';

-- 2. 创建用户留言表
CREATE TABLE IF NOT EXISTS user_feedback (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  user_id VARCHAR(20) COMMENT '用户ID（可为空，支持未登录用户留言）',
  name VARCHAR(50) NOT NULL COMMENT '留言人姓名',
  contact VARCHAR(20) NOT NULL COMMENT '联系方式（手机号）',
  message TEXT NOT NULL COMMENT '留言内容',
  status ENUM('pending', 'processing', 'resolved') DEFAULT 'pending' COMMENT '处理状态',
  admin_reply TEXT COMMENT '管理员回复',
  create_time BIGINT NOT NULL COMMENT '创建时间',
  update_time BIGINT NOT NULL COMMENT '更新时间',
  INDEX idx_user_id (user_id),
  INDEX idx_status (status),
  INDEX idx_create_time (create_time),
  INDEX idx_contact (contact)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户留言表';

-- 3. 插入上海晚歌集团信息
INSERT INTO company_info (
  company_name,
  company_description,
  company_address,
  company_phone,
  company_type,
  established_date,
  company_status,
  createTime,
  updateTime
) VALUES (
  '上海晚歌集团',
  '上海晚歌企业发展集团，初创于2010年。经过11年的发展，历享凡贸易、晚歌家纺、鑫策财税等不同的业务项目后组建集团公司，并不断扩大集团业务范围，尤其是最近三年，在公司全体同仁的努力，政府机关、社会各界人士的支持，以及广大客户的信任下，发展速度日渐加快。

当前集团旗下已逐渐形成包括（运营主体为"鑫策(上海)企业登记代理有限公司"的）工商税务代理、（运营主体为"上海国巹企业管理有限公司"的）知识产权、（运营主体为"上海鑫心征信服务有限公司"的）企业资质、（运营主体为"上海今嘉人力资源有限公司"的）人事代理、（运营主体为"猎优企（上海）企业发展有限公司"的）企业转让等主要业务板块；团队规模达到近50人；办公场地包括总部及工业园区分部室等超过700平米；积累各项业务的客户超过5000家，在行业中已具备相当的知名度和影响力。

新的十年，公司提出更高的目标和发展战略，力将进一步升级公司现有业务模型，构建新的商业模式，勇做行业创新先行者，建立行业业务标准规范；筹备业务将延伸至涵盖金融财务咨询、企业管理咨询、人力资源服务、融资上市服务等全方位企业服务。

有鉴于此，集团需要也欢迎社会各界有识有志之士加盟，群策群力，不断实现新的发展目标；公司深知人才对企业发展的重要性，相比很多企业只关心你能为它做什么，我们非常在意公司能如何帮助你成长。

相信集团的未来一定会更加美好！也愿你的未来同样精彩！',
  '上海市黄浦区陆家浜路1332号南开大厦805室',
  '************',
  '集团公司',
  '2010-01-01',
  'active',
  UNIX_TIMESTAMP() * 1000,
  UNIX_TIMESTAMP() * 1000
) ON DUPLICATE KEY UPDATE
  company_address = VALUES(company_address),
  company_phone = VALUES(company_phone),
  updateTime = VALUES(updateTime);

-- 4. 验证表创建结果
SELECT 'Tables created successfully' as status;

-- 查看公司信息表结构
DESCRIBE company_info;

-- 查看用户留言表结构
DESCRIBE user_feedback;

-- 查看公司信息数据
SELECT company_name, company_address, company_phone FROM company_info;
