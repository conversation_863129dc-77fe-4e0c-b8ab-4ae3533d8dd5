const pem = require('pem');
const fs = require('fs');
const path = require('path');

const certDir = path.join(__dirname, '../cert');

// 创建证书目录
if (!fs.existsSync(certDir)) {
  fs.mkdirSync(certDir, { recursive: true });
}

// 生成证书
pem.createCertificate({ days: 365, selfSigned: true }, (err, keys) => {
  if (err) {
    console.error('生成证书失败:', err);
    return;
  }

  // 保存私钥
  fs.writeFileSync(path.join(certDir, 'private.key'), keys.serviceKey);
  
  // 保存证书
  fs.writeFileSync(path.join(certDir, 'certificate.crt'), keys.certificate);

  console.log('SSL证书已生成！');
}); 