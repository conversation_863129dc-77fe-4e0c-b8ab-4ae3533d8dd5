-- 添加群公告字段到groups表
-- 此SQL可以在微信云托管的数据库管理界面中直接执行

-- 检查并添加announcement字段
SET @sql = IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE TABLE_SCHEMA = DATABASE() 
   AND TABLE_NAME = 'groups' 
   AND COLUMN_NAME = 'announcement') = 0,
  'ALTER TABLE `groups` ADD COLUMN `announcement` TEXT COMMENT ''群公告'' AFTER `description`',
  'SELECT ''announcement字段已存在，无需添加'' AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为现有群组设置默认公告（如果字段刚刚添加）
UPDATE `groups` 
SET `announcement` = '欢迎加入本群！' 
WHERE `announcement` IS NULL OR `announcement` = '';

-- 验证字段是否添加成功
SELECT 
  COLUMN_NAME,
  DATA_TYPE,
  IS_NULLABLE,
  COLUMN_DEFAULT,
  COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'groups' 
  AND COLUMN_NAME = 'announcement';

-- 查看groups表的完整结构
DESCRIBE `groups`;

-- 查看现有群组数据（验证announcement字段）
SELECT 
  id,
  name,
  description,
  announcement,
  creatorId,
  createTime
FROM `groups` 
LIMIT 5;
