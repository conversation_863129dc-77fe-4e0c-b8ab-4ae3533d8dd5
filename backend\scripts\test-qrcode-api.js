/**
 * 测试二维码API
 */
const axios = require('axios');

// 配置
const BASE_URL = 'https://lieyouqi-158837-8-**********.sh.run.tcloudbase.com';
const TEST_SCENE = 'test_' + Date.now();

async function testQrcodeAPI() {
  console.log('开始测试二维码API...');
  console.log('测试URL:', `${BASE_URL}/api/system/qrcode`);
  console.log('测试参数:', { scene: TEST_SCENE });

  try {
    // 测试健康检查
    console.log('\n1. 测试健康检查...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('健康检查结果:', healthResponse.data);

    // 测试二维码生成
    console.log('\n2. 测试二维码生成...');
    const qrcodeResponse = await axios.get(`${BASE_URL}/api/system/qrcode`, {
      params: { scene: TEST_SCENE },
      timeout: 30000 // 30秒超时
    });

    console.log('二维码API响应状态:', qrcodeResponse.status);
    console.log('二维码API响应数据:', qrcodeResponse.data);

    if (qrcodeResponse.data.success && qrcodeResponse.data.qrcodeUrl) {
      console.log('\n✅ 二维码生成成功!');
      console.log('二维码URL:', qrcodeResponse.data.qrcodeUrl);

      // 测试二维码图片是否可访问
      const fullQrcodeUrl = qrcodeResponse.data.qrcodeUrl.startsWith('http')
        ? qrcodeResponse.data.qrcodeUrl
        : BASE_URL + qrcodeResponse.data.qrcodeUrl;

      console.log('\n3. 测试二维码图片访问...');
      const imageResponse = await axios.head(fullQrcodeUrl);
      console.log('图片访问状态:', imageResponse.status);
      console.log('图片Content-Type:', imageResponse.headers['content-type']);

      if (imageResponse.headers['content-type']?.includes('image')) {
        console.log('✅ 二维码图片可正常访问!');
      } else {
        console.log('❌ 二维码图片访问异常');
      }
    } else {
      console.log('❌ 二维码生成失败:', qrcodeResponse.data);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);

    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }

    if (error.code === 'ECONNREFUSED') {
      console.error('连接被拒绝，请检查服务是否正在运行');
    } else if (error.code === 'ENOTFOUND') {
      console.error('域名解析失败，请检查网络连接');
    } else if (error.code === 'ETIMEDOUT') {
      console.error('请求超时，服务可能正在启动中');
    }
  }
}

// 运行测试
testQrcodeAPI().then(() => {
  console.log('\n测试完成');
}).catch(error => {
  console.error('测试执行失败:', error);
});
