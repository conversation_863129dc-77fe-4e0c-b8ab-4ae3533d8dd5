/**
 * 为群聊相关表添加必要字段的脚本
 * 这个脚本会尝试添加每个字段，如果字段已存在则会跳过
 */
require('dotenv').config();
const mysql = require('mysql2/promise');

// 微信云托管环境变量优先
const dbHost = process.env.MYSQL_IP || process.env.DB_HOST || 'localhost';
const dbPort = process.env.MYSQL_PORT || 3306;
const dbUser = process.env.MYSQL_USERNAME || process.env.DB_USER || 'root';
const dbName = process.env.MYSQL_DATABASE || process.env.DB_NAME || 'lieyouqi';
const dbPassword = (process.env.MYSQL_PASSWORD || process.env.DB_PASSWORD || '').replace(/"/g, '');

console.log('数据库配置:');
console.log('- 主机:', dbHost);
console.log('- 端口:', dbPort);
console.log('- 用户:', dbUser);
console.log('- 数据库:', dbName);
console.log('- 密码长度:', dbPassword.length);

/**
 * 安全地添加字段，如果字段已存在则跳过
 */
async function addColumnIfNotExists(connection, table, column, definition) {
  try {
    // 检查字段是否存在
    const [columns] = await connection.query(`SHOW COLUMNS FROM \`${table}\` LIKE '${column}'`);
    
    if (columns.length === 0) {
      // 字段不存在，添加它
      await connection.query(`ALTER TABLE \`${table}\` ADD COLUMN \`${column}\` ${definition}`);
      console.log(`成功添加字段 ${table}.${column}`);
      return true;
    } else {
      console.log(`字段 ${table}.${column} 已存在，跳过`);
      return false;
    }
  } catch (error) {
    console.error(`添加字段 ${table}.${column} 失败:`, error.message);
    return false;
  }
}

async function addGroupFields() {
  let connection;
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: dbHost,
      port: dbPort,
      user: dbUser,
      password: dbPassword,
      database: dbName
    });
    
    console.log('数据库连接成功!');

    // 为groups表添加字段
    await addColumnIfNotExists(connection, 'groups', 'visible', 
      'TINYINT(1) NOT NULL DEFAULT 1 COMMENT "是否可见（与isPublic同义）" AFTER `isPublic`');
    
    await addColumnIfNotExists(connection, 'groups', 'needApprove', 
      'TINYINT(1) NOT NULL DEFAULT 0 COMMENT "是否需要审批加入" AFTER `visible`');
    
    await addColumnIfNotExists(connection, 'groups', 'status', 
      'VARCHAR(20) NOT NULL DEFAULT "active" COMMENT "群组状态" AFTER `memberCount`');
    
    await addColumnIfNotExists(connection, 'groups', 'lastMessageTime', 
      'BIGINT DEFAULT NULL COMMENT "最后消息时间" AFTER `updateTime`');
    
    await addColumnIfNotExists(connection, 'groups', 'lastMessageContent', 
      'VARCHAR(255) DEFAULT NULL COMMENT "最后消息内容预览" AFTER `lastMessageTime`');

    // 为group_members表添加字段
    await addColumnIfNotExists(connection, 'group_members', 'status', 
      'VARCHAR(20) NOT NULL DEFAULT "active" COMMENT "成员状态" AFTER `role`');
    
    await addColumnIfNotExists(connection, 'group_members', 'lastReadTime', 
      'BIGINT DEFAULT NULL COMMENT "最后阅读时间" AFTER `joinTime`');

    // 为group_messages表添加字段
    await addColumnIfNotExists(connection, 'group_messages', 'status', 
      'VARCHAR(20) NOT NULL DEFAULT "normal" COMMENT "消息状态" AFTER `type`');

    // 更新现有数据，确保冗余字段同步
    try {
      await connection.query('UPDATE `groups` SET `visible` = `isPublic` WHERE 1');
      console.log('更新现有数据成功');
    } catch (updateError) {
      console.error('更新现有数据失败:', updateError.message);
    }

    console.log('所有字段添加完成!');
  } catch (error) {
    console.error('添加字段失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 执行添加字段操作
addGroupFields();
