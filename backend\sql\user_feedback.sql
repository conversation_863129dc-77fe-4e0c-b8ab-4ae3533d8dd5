-- 用户留言表创建脚本
-- 用于存储用户通过客服中心提交的留言

USE lieyouqi;

-- 创建用户留言表
CREATE TABLE IF NOT EXISTS user_feedback (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  user_id VARCHAR(20) COMMENT '用户ID（可为空，支持未登录用户留言）',
  name VARCHAR(50) NOT NULL COMMENT '留言人姓名',
  contact VARCHAR(20) NOT NULL COMMENT '联系方式（手机号）',
  message TEXT NOT NULL COMMENT '留言内容',
  status ENUM('pending', 'processing', 'resolved') DEFAULT 'pending' COMMENT '处理状态',
  admin_reply TEXT COMMENT '管理员回复',
  create_time BIGINT NOT NULL COMMENT '创建时间',
  update_time BIGINT NOT NULL COMMENT '更新时间',
  INDEX idx_user_id (user_id),
  INDEX idx_status (status),
  INDEX idx_create_time (create_time),
  INDEX idx_contact (contact)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户留言表';

-- 查看表结构
DESCRIBE user_feedback;
