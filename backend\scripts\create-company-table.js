/**
 * 创建公司信息表脚本
 * 用于在现有数据库中添加公司信息表
 */
const db = require('../config/db');

async function createCompanyTable() {
  try {
    console.log('开始创建公司信息表...');

    // 创建公司信息表
    await db.query(`
      CREATE TABLE IF NOT EXISTS company_info (
        id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
        company_name VARCHAR(100) NOT NULL COMMENT '公司名称',
        company_description TEXT COMMENT '公司简介',
        company_address VARCHAR(255) COMMENT '公司地址',
        company_phone VARCHAR(50) COMMENT '公司电话',
        company_email VARCHAR(100) COMMENT '公司邮箱',
        company_website VARCHAR(255) COMMENT '公司网站',
        business_hours VARCHAR(100) COMMENT '营业时间',
        company_logo VARCHAR(255) COMMENT '公司Logo',
        contact_person VARCHAR(50) COMMENT '联系人',
        fax VARCHAR(50) COMMENT '传真号码',
        postal_code VARCHAR(20) COMMENT '邮政编码',
        company_type VARCHAR(50) COMMENT '公司类型',
        established_date DATE COMMENT '成立日期',
        registration_number VARCHAR(100) COMMENT '注册号码',
        social_credit_code VARCHAR(100) COMMENT '统一社会信用代码',
        legal_representative VARCHAR(50) COMMENT '法定代表人',
        registered_capital DECIMAL(15,2) COMMENT '注册资本',
        business_scope TEXT COMMENT '经营范围',
        company_status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '公司状态',
        createTime BIGINT NOT NULL COMMENT '创建时间',
        updateTime BIGINT NOT NULL COMMENT '更新时间',
        INDEX idx_company_name (company_name),
        INDEX idx_create_time (createTime),
        INDEX idx_update_time (updateTime)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司信息表'
    `);

    console.log('公司信息表创建成功！');

    // 检查表是否创建成功
    const [tables] = await db.query('SHOW TABLES LIKE "company_info"');
    if (tables.length > 0) {
      console.log('✓ 公司信息表已存在于数据库中');
      
      // 显示表结构
      const [columns] = await db.query('DESCRIBE company_info');
      console.log('\n表结构:');
      console.table(columns);
    } else {
      console.log('✗ 公司信息表创建失败');
    }

    // 插入示例数据（可选）
    const insertSample = process.argv.includes('--with-sample');
    if (insertSample) {
      console.log('\n插入示例数据...');
      
      // 检查是否已有数据
      const [existingData] = await db.query('SELECT COUNT(*) as count FROM company_info');
      if (existingData[0].count === 0) {
        await db.query(`
          INSERT INTO company_info (
            company_name,
            company_description,
            company_address,
            company_phone,
            company_email,
            company_website,
            business_hours,
            contact_person,
            company_type,
            company_status,
            createTime,
            updateTime
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          '示例科技有限公司',
          '这是一家专注于技术创新的科技公司，致力于为客户提供优质的技术解决方案。',
          '北京市朝阳区示例大厦1001室',
          '010-12345678',
          '<EMAIL>',
          'https://www.example.com',
          '周一至周五 9:00-18:00',
          '张经理',
          '有限责任公司',
          'active',
          Date.now(),
          Date.now()
        ]);
        
        console.log('✓ 示例数据插入成功');
      } else {
        console.log('✓ 表中已有数据，跳过示例数据插入');
      }
    }

    console.log('\n公司信息表创建完成！');
    console.log('\n可用的API接口:');
    console.log('- GET /api/company/info - 获取公司信息');
    console.log('- GET /api/company/list - 获取所有公司信息');
    console.log('- POST /api/company/create - 创建公司信息（需要认证）');
    console.log('- PUT /api/company/update/:id - 更新公司信息（需要认证）');
    console.log('- DELETE /api/company/delete/:id - 删除公司信息（需要认证）');
    console.log('- GET /api/company/search?name=关键词 - 搜索公司信息');

  } catch (error) {
    console.error('创建公司信息表失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  createCompanyTable()
    .then(() => {
      console.log('\n脚本执行完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = {
  createCompanyTable
};
