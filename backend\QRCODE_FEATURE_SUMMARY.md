# 分享页面生成带参数二维码功能总结

## 功能描述
实现分享页面生成带推荐人ID参数的小程序二维码，用于推荐新用户注册。

## 核心文件

### 1. 前端分享页面
- `miniprogram/pages/share/share.js` - 分享页面逻辑
- `miniprogram/pages/share/share.wxml` - 分享页面模板
- `miniprogram/pages/share/share.wxss` - 分享页面样式

### 2. 后端二维码服务
- `backend/services/qrcodeService.js` - 二维码生成服务
- `backend/controllers/qrcodeController.js` - 二维码控制器
- `backend/routes/system.js` - 系统路由（包含二维码API）

### 3. 认证页面
- `miniprogram/pages/auth/auth.js` - 处理推荐参数的认证页面

## 关键配置

### 1. 微信环境变量 (container.config.json)
```json
{
  "envParams": {
    "WX_APPID": "{{WX_APPID}}",
    "WX_SECRET": "{{WX_SECRET}}"
  }
}
```

### 2. 二维码API端点
```
GET /api/system/qrcode?scene={推荐人ID}
```

## 功能流程

1. **用户进入分享页面**
   - 获取当前用户信息作为推荐人
   - 调用后端API生成带推荐人ID的二维码

2. **二维码生成**
   - 后端调用微信小程序码API
   - 生成指向 `pages/auth/auth` 页面的二维码
   - 二维码包含推荐人ID参数

3. **新用户扫码**
   - 跳转到认证页面
   - 自动提取推荐人ID参数
   - 保存到本地存储供后续使用

4. **分享图片生成**
   - 使用Canvas合成包含二维码的分享图片
   - 支持保存到相册和直接分享

## 部署要求

1. **微信小程序配置**
   - 确保 WX_APPID 和 WX_SECRET 正确配置
   - 小程序已发布并可正常访问

2. **服务器配置**
   - 确保 `/public/qrcode` 目录可写
   - 静态文件服务正常

3. **网络配置**
   - 服务器可访问微信API
   - 二维码图片可通过HTTP访问

## 测试方法

1. **本地测试**
   ```bash
   # 测试二维码API
   curl "https://your-domain.com/api/system/qrcode?scene=test123"
   ```

2. **小程序测试**
   - 进入分享页面
   - 检查二维码是否正常生成
   - 扫码测试跳转是否正确

## 注意事项

1. 二维码生成需要有效的微信小程序凭证
2. 推荐参数通过 `scene` 参数传递
3. 新用户落地页为 `pages/auth/auth`
4. 二维码文件保存在 `public/qrcode` 目录
