# 公司信息表使用说明

## 概述

公司信息表（`company_info`）用于存储公司的各项基本信息，包括公司简介、地址、联系方式等详细信息。

## 表结构

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | INT | - | 是 | AUTO_INCREMENT | 主键ID |
| company_name | VARCHAR | 100 | 是 | - | 公司名称 |
| company_description | TEXT | - | 否 | - | 公司简介 |
| company_address | VARCHAR | 255 | 否 | - | 公司地址 |
| company_phone | VARCHAR | 50 | 否 | - | 公司电话 |
| company_email | VARCHAR | 100 | 否 | - | 公司邮箱 |
| company_website | VARCHAR | 255 | 否 | - | 公司网站 |
| business_hours | VARCHAR | 100 | 否 | - | 营业时间 |
| company_logo | VARCHAR | 255 | 否 | - | 公司Logo |
| contact_person | VARCHAR | 50 | 否 | - | 联系人 |
| fax | VARCHAR | 50 | 否 | - | 传真号码 |
| postal_code | VARCHAR | 20 | 否 | - | 邮政编码 |
| company_type | VARCHAR | 50 | 否 | - | 公司类型 |
| established_date | DATE | - | 否 | - | 成立日期 |
| registration_number | VARCHAR | 100 | 否 | - | 注册号码 |
| social_credit_code | VARCHAR | 100 | 否 | - | 统一社会信用代码 |
| legal_representative | VARCHAR | 50 | 否 | - | 法定代表人 |
| registered_capital | DECIMAL | 15,2 | 否 | - | 注册资本 |
| business_scope | TEXT | - | 否 | - | 经营范围 |
| company_status | ENUM | - | 否 | active | 公司状态（active/inactive/suspended） |
| createTime | BIGINT | - | 是 | - | 创建时间 |
| updateTime | BIGINT | - | 是 | - | 更新时间 |

## 创建表

### 方法一：使用脚本创建

```bash
# 仅创建表
node scripts/create-company-table.js

# 创建表并插入示例数据
node scripts/create-company-table.js --with-sample
```

### 方法二：执行SQL文件

```bash
# 在MySQL中执行
mysql -u root -p lieyouqi < sql/company_info.sql
```

### 方法三：通过数据库初始化

```bash
# 重新初始化数据库（会创建所有表包括公司信息表）
npm run init-mysql-db
```

## API接口

### 1. 获取公司信息

**接口地址：** `GET /api/company/info/:id?`

**说明：** 获取指定ID的公司信息，如果不传ID则获取第一条记录

**请求参数：**
- `id`（可选）：公司信息ID

**响应示例：**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "company_name": "示例科技有限公司",
    "company_description": "这是一家专注于技术创新的科技公司",
    "company_address": "北京市朝阳区示例大厦1001室",
    "company_phone": "010-12345678",
    "company_email": "<EMAIL>",
    "company_website": "https://www.example.com",
    "business_hours": "周一至周五 9:00-18:00",
    "contact_person": "张经理",
    "company_type": "有限责任公司",
    "company_status": "active",
    "createTime": 1703123456789,
    "updateTime": 1703123456789
  }
}
```

### 2. 获取所有公司信息

**接口地址：** `GET /api/company/list`

**说明：** 获取所有公司信息列表

**响应示例：**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "company_name": "示例科技有限公司",
      // ... 其他字段
    }
  ]
}
```

### 3. 创建公司信息

**接口地址：** `POST /api/company/create`

**说明：** 创建新的公司信息（需要认证）

**请求头：**
```
Authorization: Bearer <token>
```

**请求体示例：**
```json
{
  "company_name": "新公司名称",
  "company_description": "公司简介",
  "company_address": "公司地址",
  "company_phone": "010-12345678",
  "company_email": "<EMAIL>",
  "company_website": "https://www.company.com",
  "business_hours": "周一至周五 9:00-18:00",
  "contact_person": "联系人姓名",
  "company_type": "有限责任公司"
}
```

### 4. 更新公司信息

**接口地址：** `PUT /api/company/update/:id`

**说明：** 更新指定ID的公司信息（需要认证）

**请求头：**
```
Authorization: Bearer <token>
```

**请求参数：**
- `id`：公司信息ID

**请求体：** 需要更新的字段（支持部分更新）

### 5. 删除公司信息

**接口地址：** `DELETE /api/company/delete/:id`

**说明：** 删除指定ID的公司信息（需要认证）

**请求头：**
```
Authorization: Bearer <token>
```

**请求参数：**
- `id`：公司信息ID

### 6. 搜索公司信息

**接口地址：** `GET /api/company/search?name=关键词`

**说明：** 根据公司名称搜索公司信息

**请求参数：**
- `name`：搜索关键词

## 使用示例

### 前端调用示例

```javascript
// 获取公司信息
const getCompanyInfo = async () => {
  try {
    const response = await fetch('/api/company/info');
    const result = await response.json();
    if (result.success) {
      console.log('公司信息:', result.data);
    }
  } catch (error) {
    console.error('获取公司信息失败:', error);
  }
};

// 创建公司信息
const createCompanyInfo = async (companyData) => {
  try {
    const response = await fetch('/api/company/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(companyData)
    });
    const result = await response.json();
    if (result.success) {
      console.log('公司信息创建成功');
    }
  } catch (error) {
    console.error('创建公司信息失败:', error);
  }
};
```

## 注意事项

1. **权限控制**：创建、更新、删除操作需要用户认证
2. **数据验证**：公司名称为必填字段
3. **时间戳**：createTime和updateTime使用毫秒级时间戳
4. **字符编码**：表使用utf8mb4字符集，支持emoji等特殊字符
5. **索引优化**：在company_name、createTime、updateTime字段上建立了索引

## 扩展建议

1. 可以根据业务需要添加更多字段
2. 可以添加公司分类、行业等分类字段
3. 可以添加公司评级、认证状态等字段
4. 可以关联用户表，记录公司信息的创建者和修改者
