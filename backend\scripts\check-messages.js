const db = require('../config/db');

async function checkMessages() {
    try {
        // 先查询所有用户
        const [users] = await db.query('SELECT id, nickname FROM users');
        console.log('所有用户：');
        if (users && users.id) {
            console.log(`ID: ${users.id}, 昵称: ${users.nickname}`);
        } else {
            console.log('没有找到任何用户');
        }
        console.log('------------------------');

        // 查询所有消息
        const [messages] = await db.query(`
            SELECT m.*, 
                   u1.nickname COLLATE utf8mb4_unicode_ci as sender_nickname, 
                   u2.nickname COLLATE utf8mb4_unicode_ci as receiver_nickname
            FROM messages m
            LEFT JOIN users u1 ON m.senderId = u1.id COLLATE utf8mb4_unicode_ci
            LEFT JOIN users u2 ON m.receiverId = u2.id COLLATE utf8mb4_unicode_ci
            ORDER BY m.createTime DESC
        `);

        console.log('所有消息：');
        if (messages && messages.id) {
            console.log('消息详情:');
            console.log(`发送者ID: ${messages.senderId}`);
            console.log(`发送者昵称: ${messages.sender_nickname}`);
            console.log(`接收者ID: ${messages.receiverId}`);
            console.log(`接收者昵称: ${messages.receiver_nickname}`);
            console.log(`内容: ${messages.content}`);
            if (messages.createTime) {
                console.log(`时间: ${new Date(messages.createTime).toLocaleString()}`);
            }
        } else {
            console.log('没有找到任何消息');
        }
    } catch (error) {
        console.error('查询失败:', error);
        console.error('错误详情:', error.message);
        if (error.sql) {
            console.error('SQL语句:', error.sql);
        }
    } finally {
        process.exit();
    }
}

checkMessages(); 