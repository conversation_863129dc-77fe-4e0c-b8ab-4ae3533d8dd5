const mysql = require('mysql2/promise');
const config = require('./config');

async function seedDatabase() {
  try {
    const connection = await mysql.createConnection({
      host: config.db.host,
      user: config.db.user,
      password: config.db.password,
      database: config.db.database
    });

    // 检查是否有数据
    const [users] = await connection.query('SELECT COUNT(*) as count FROM users');
    if (users[0].count > 0) {
      console.log('数据库已有数据，跳过种子数据插入');
      await connection.end();
      return;
    }

    // 插入测试用户
    await connection.query(`
      INSERT INTO users (id, username, password, nickname, createTime)
      VALUES 
        ('1', 'test1', 'password1', '测试用户1', ${Date.now()}),
        ('2', 'test2', 'password2', '测试用户2', ${Date.now()})
    `);

    // 插入测试帖子
    await connection.query(`
      INSERT INTO posts (id, userId, content, createTime)
      VALUES 
        ('1', '1', '这是测试帖子1', ${Date.now()}),
        ('2', '2', '这是测试帖子2', ${Date.now()})
    `);

    await connection.end();
    console.log('数据库种子数据插入完成');
    return true;
  } catch (error) {
    console.error('数据库种子数据插入失败:', error);
    throw error;
  }
}

module.exports = {
  seedDatabase
}; 