/**
 * 测试公司信息表功能
 */
const CompanyInfo = require('../models/CompanyInfo');

async function testCompanyTable() {
  try {
    console.log('开始测试公司信息表功能...\n');

    // 测试1: 创建公司信息
    console.log('1. 测试创建公司信息');
    const testCompanyData = {
      company_name: '测试科技有限公司',
      company_description: '这是一个测试公司，用于验证公司信息表的功能。',
      company_address: '上海市浦东新区测试大厦888号',
      company_phone: '021-88888888',
      company_email: '<EMAIL>',
      company_website: 'https://www.testcompany.com',
      business_hours: '周一至周五 8:30-17:30',
      contact_person: '李测试',
      company_type: '有限责任公司',
      fax: '021-88888889',
      postal_code: '200000',
      established_date: '2020-01-01',
      registration_number: 'TEST123456789',
      social_credit_code: '91TEST123456789',
      legal_representative: '李总',
      registered_capital: 1000000.00,
      business_scope: '技术开发、技术咨询、技术服务',
      company_status: 'active'
    };

    const createResult = await CompanyInfo.create(testCompanyData);
    console.log('创建结果:', createResult);

    if (createResult.success) {
      const companyId = createResult.id;
      console.log(`✓ 公司信息创建成功，ID: ${companyId}\n`);

      // 测试2: 根据ID获取公司信息
      console.log('2. 测试根据ID获取公司信息');
      const companyById = await CompanyInfo.findById(companyId);
      console.log('获取结果:', companyById ? '成功' : '失败');
      if (companyById) {
        console.log(`✓ 公司名称: ${companyById.company_name}`);
        console.log(`✓ 公司地址: ${companyById.company_address}\n`);
      }

      // 测试3: 获取所有公司信息
      console.log('3. 测试获取所有公司信息');
      const allCompanies = await CompanyInfo.findAll();
      console.log(`✓ 共找到 ${allCompanies.length} 条公司信息\n`);

      // 测试4: 更新公司信息
      console.log('4. 测试更新公司信息');
      const updateData = {
        company_description: '这是一个更新后的测试公司描述。',
        company_phone: '021-99999999'
      };
      const updateResult = await CompanyInfo.update(companyId, updateData);
      console.log('更新结果:', updateResult);
      if (updateResult.success) {
        console.log('✓ 公司信息更新成功\n');
      }

      // 测试5: 搜索公司信息
      console.log('5. 测试搜索公司信息');
      const searchResults = await CompanyInfo.searchByName('测试');
      console.log(`✓ 搜索"测试"找到 ${searchResults.length} 条结果\n`);

      // 测试6: 获取默认公司信息（不传ID）
      console.log('6. 测试获取默认公司信息');
      const defaultCompany = await CompanyInfo.findById();
      console.log('默认公司信息:', defaultCompany ? '获取成功' : '无数据');
      if (defaultCompany) {
        console.log(`✓ 默认公司: ${defaultCompany.company_name}\n`);
      }

      // 测试7: 删除测试数据（清理）
      console.log('7. 清理测试数据');
      const deleteResult = await CompanyInfo.delete(companyId);
      console.log('删除结果:', deleteResult);
      if (deleteResult.success) {
        console.log('✓ 测试数据清理成功\n');
      }

    } else {
      console.log('✗ 公司信息创建失败，跳过后续测试\n');
    }

    console.log('所有测试完成！');

  } catch (error) {
    console.error('测试过程中发生错误:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testCompanyTable()
    .then(() => {
      console.log('\n测试脚本执行完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('测试脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = {
  testCompanyTable
};
