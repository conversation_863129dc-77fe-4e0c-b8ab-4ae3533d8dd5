-- 公司信息表创建脚本
-- 用于存储公司的各项基本信息

USE lieyouqi;

-- 创建公司信息表
CREATE TABLE IF NOT EXISTS company_info (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  company_name VARCHAR(100) NOT NULL COMMENT '公司名称',
  company_description TEXT COMMENT '公司简介',
  company_address VARCHAR(255) COMMENT '公司地址',
  company_phone VARCHAR(50) COMMENT '公司电话',
  company_email VARCHAR(100) COMMENT '公司邮箱',
  company_website VARCHAR(255) COMMENT '公司网站',
  business_hours VARCHAR(100) COMMENT '营业时间',
  company_logo VARCHAR(255) COMMENT '公司Logo',
  contact_person VARCHAR(50) COMMENT '联系人',
  fax VARCHAR(50) COMMENT '传真号码',
  postal_code VARCHAR(20) COMMENT '邮政编码',
  company_type VARCHAR(50) COMMENT '公司类型',
  established_date DATE COMMENT '成立日期',
  registration_number VARCHAR(100) COMMENT '注册号码',
  social_credit_code VARCHAR(100) COMMENT '统一社会信用代码',
  legal_representative VARCHAR(50) COMMENT '法定代表人',
  registered_capital DECIMAL(15,2) COMMENT '注册资本',
  business_scope TEXT COMMENT '经营范围',
  company_status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '公司状态',
  createTime BIGINT NOT NULL COMMENT '创建时间',
  updateTime BIGINT NOT NULL COMMENT '更新时间',
  INDEX idx_company_name (company_name),
  INDEX idx_create_time (createTime),
  INDEX idx_update_time (updateTime)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司信息表';

-- 插入示例数据（可选）
INSERT INTO company_info (
  company_name,
  company_description,
  company_address,
  company_phone,
  company_email,
  company_website,
  business_hours,
  contact_person,
  company_type,
  company_status,
  createTime,
  updateTime
) VALUES (
  '示例科技有限公司',
  '这是一家专注于技术创新的科技公司，致力于为客户提供优质的技术解决方案。',
  '北京市朝阳区示例大厦1001室',
  '010-12345678',
  '<EMAIL>',
  'https://www.example.com',
  '周一至周五 9:00-18:00',
  '张经理',
  '有限责任公司',
  'active',
  UNIX_TIMESTAMP() * 1000,
  UNIX_TIMESTAMP() * 1000
);

-- 查看表结构
DESCRIBE company_info;

-- 查看数据
SELECT * FROM company_info;
