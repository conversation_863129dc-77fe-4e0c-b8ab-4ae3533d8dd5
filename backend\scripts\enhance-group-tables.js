/**
 * 增强群聊相关表结构的脚本
 * 添加必要的冗余字段，提高系统兼容性和扩展性
 */
require('dotenv').config();
const mysql = require('mysql2/promise');

// 微信云托管环境变量优先
const dbHost = process.env.MYSQL_IP || process.env.DB_HOST || 'localhost';
const dbPort = process.env.MYSQL_PORT || 3306;
const dbUser = process.env.MYSQL_USERNAME || process.env.DB_USER || 'root';
const dbName = process.env.MYSQL_DATABASE || process.env.DB_NAME || 'lieyouqi';
const dbPassword = (process.env.MYSQL_PASSWORD || process.env.DB_PASSWORD || '').replace(/"/g, '');

console.log('数据库配置:');
console.log('- 主机:', dbHost);
console.log('- 端口:', dbPort);
console.log('- 用户:', dbUser);
console.log('- 数据库:', dbName);
console.log('- 密码长度:', dbPassword.length);

async function enhanceGroupTables() {
  let connection;
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: dbHost,
      port: dbPort,
      user: dbUser,
      password: dbPassword,
      database: dbName,
      multipleStatements: true // 允许执行多条SQL语句
    });
    
    console.log('数据库连接成功!');

    // 1. 增强 groups 表
    console.log('开始增强 groups 表...');
    await connection.query(`
      ALTER TABLE \`groups\` 
      ADD COLUMN IF NOT EXISTS \`visible\` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否可见（与isPublic同义）' AFTER \`isPublic\`,
      ADD COLUMN IF NOT EXISTS \`needApprove\` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否需要审批加入' AFTER \`visible\`,
      ADD COLUMN IF NOT EXISTS \`status\` ENUM('active', 'locked', 'deleted') NOT NULL DEFAULT 'active' COMMENT '群组状态' AFTER \`memberCount\`,
      ADD COLUMN IF NOT EXISTS \`lastMessageTime\` BIGINT DEFAULT NULL COMMENT '最后消息时间' AFTER \`updateTime\`,
      ADD COLUMN IF NOT EXISTS \`lastMessageContent\` VARCHAR(255) DEFAULT NULL COMMENT '最后消息内容预览' AFTER \`lastMessageTime\`;
    `);
    console.log('groups 表增强完成');

    // 2. 增强 group_members 表
    console.log('开始增强 group_members 表...');
    await connection.query(`
      ALTER TABLE \`group_members\` 
      ADD COLUMN IF NOT EXISTS \`status\` ENUM('active', 'muted', 'blocked') NOT NULL DEFAULT 'active' COMMENT '成员状态' AFTER \`role\`,
      ADD COLUMN IF NOT EXISTS \`lastReadTime\` BIGINT DEFAULT NULL COMMENT '最后阅读时间' AFTER \`joinTime\`;
    `);
    console.log('group_members 表增强完成');

    // 3. 增强 group_messages 表
    console.log('开始增强 group_messages 表...');
    await connection.query(`
      ALTER TABLE \`group_messages\` 
      ADD COLUMN IF NOT EXISTS \`status\` ENUM('normal', 'recalled', 'deleted') NOT NULL DEFAULT 'normal' COMMENT '消息状态' AFTER \`type\`;
    `);
    console.log('group_messages 表增强完成');

    // 4. 更新现有数据，确保冗余字段同步
    console.log('开始更新现有数据...');
    await connection.query(`
      UPDATE \`groups\` SET \`visible\` = \`isPublic\` WHERE 1;
    `);
    console.log('现有数据更新完成');

    console.log('所有群聊相关表结构增强完成!');
  } catch (error) {
    console.error('增强群聊表结构失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 执行增强操作
enhanceGroupTables();
