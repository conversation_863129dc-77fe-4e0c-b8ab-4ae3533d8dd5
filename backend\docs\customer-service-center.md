# 客服中心功能完善说明

## 📋 完成的工作

### 1. 数据库表创建
- ✅ **公司信息表 (company_info)**: 存储公司基本信息
- ✅ **用户留言表 (user_feedback)**: 存储用户通过客服中心提交的留言

### 2. 后端API接口
- ✅ **获取公司信息**: `GET /api/company/info`
- ✅ **提交客服留言**: `POST /api/system/feedback`
- ✅ **公司信息管理**: 完整的CRUD操作

### 3. 前端功能完善
- ✅ **动态加载公司联系方式**: 从数据库读取地址、电话、邮箱
- ✅ **留言提交功能**: 支持用户提交客服留言
- ✅ **一键拨打电话**: 点击电话号码直接拨打
- ✅ **表单验证**: 完整的输入验证和错误提示

## 🗃️ 数据库部署

### 在微信云托管中执行以下SQL命令：

```sql
-- 创建客服中心相关表
USE lieyouqi;

-- 1. 创建公司信息表
CREATE TABLE IF NOT EXISTS company_info (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  company_name VARCHAR(100) NOT NULL COMMENT '公司名称',
  company_description TEXT COMMENT '公司简介',
  company_address VARCHAR(255) COMMENT '公司地址',
  company_phone VARCHAR(50) COMMENT '公司电话',
  company_email VARCHAR(100) COMMENT '公司邮箱',
  company_website VARCHAR(255) COMMENT '公司网站',
  business_hours VARCHAR(100) COMMENT '营业时间',
  company_logo VARCHAR(255) COMMENT '公司Logo',
  contact_person VARCHAR(50) COMMENT '联系人',
  fax VARCHAR(50) COMMENT '传真号码',
  postal_code VARCHAR(20) COMMENT '邮政编码',
  company_type VARCHAR(50) COMMENT '公司类型',
  established_date DATE COMMENT '成立日期',
  registration_number VARCHAR(100) COMMENT '注册号码',
  social_credit_code VARCHAR(100) COMMENT '统一社会信用代码',
  legal_representative VARCHAR(50) COMMENT '法定代表人',
  registered_capital DECIMAL(15,2) COMMENT '注册资本',
  business_scope TEXT COMMENT '经营范围',
  company_status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '公司状态',
  createTime BIGINT NOT NULL COMMENT '创建时间',
  updateTime BIGINT NOT NULL COMMENT '更新时间',
  INDEX idx_company_name (company_name),
  INDEX idx_create_time (createTime),
  INDEX idx_update_time (updateTime)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司信息表';

-- 2. 创建用户留言表
CREATE TABLE IF NOT EXISTS user_feedback (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  user_id VARCHAR(20) COMMENT '用户ID（可为空，支持未登录用户留言）',
  name VARCHAR(50) NOT NULL COMMENT '留言人姓名',
  contact VARCHAR(20) NOT NULL COMMENT '联系方式（手机号）',
  message TEXT NOT NULL COMMENT '留言内容',
  status ENUM('pending', 'processing', 'resolved') DEFAULT 'pending' COMMENT '处理状态',
  admin_reply TEXT COMMENT '管理员回复',
  create_time BIGINT NOT NULL COMMENT '创建时间',
  update_time BIGINT NOT NULL COMMENT '更新时间',
  INDEX idx_user_id (user_id),
  INDEX idx_status (status),
  INDEX idx_create_time (create_time),
  INDEX idx_contact (contact)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户留言表';

-- 3. 插入上海晚歌集团信息
INSERT INTO company_info (
  company_name,
  company_description,
  company_address,
  company_phone,
  company_type,
  established_date,
  company_status,
  createTime,
  updateTime
) VALUES (
  '上海晚歌集团',
  '上海晚歌企业发展集团，初创于2010年。经过11年的发展，历享凡贸易、晚歌家纺、鑫策财税等不同的业务项目后组建集团公司，并不断扩大集团业务范围，尤其是最近三年，在公司全体同仁的努力，政府机关、社会各界人士的支持，以及广大客户的信任下，发展速度日渐加快。

当前集团旗下已逐渐形成包括（运营主体为"鑫策(上海)企业登记代理有限公司"的）工商税务代理、（运营主体为"上海国巹企业管理有限公司"的）知识产权、（运营主体为"上海鑫心征信服务有限公司"的）企业资质、（运营主体为"上海今嘉人力资源有限公司"的）人事代理、（运营主体为"猎优企（上海）企业发展有限公司"的）企业转让等主要业务板块；团队规模达到近50人；办公场地包括总部及工业园区分部室等超过700平米；积累各项业务的客户超过5000家，在行业中已具备相当的知名度和影响力。

新的十年，公司提出更高的目标和发展战略，力将进一步升级公司现有业务模型，构建新的商业模式，勇做行业创新先行者，建立行业业务标准规范；筹备业务将延伸至涵盖金融财务咨询、企业管理咨询、人力资源服务、融资上市服务等全方位企业服务。

有鉴于此，集团需要也欢迎社会各界有识有志之士加盟，群策群力，不断实现新的发展目标；公司深知人才对企业发展的重要性，相比很多企业只关心你能为它做什么，我们非常在意公司能如何帮助你成长。

相信集团的未来一定会更加美好！也愿你的未来同样精彩！',
  '上海市黄浦区陆家浜路1332号南开大厦805室',
  '400-788-3600',
  '集团公司',
  '2010-01-01',
  'active',
  UNIX_TIMESTAMP() * 1000,
  UNIX_TIMESTAMP() * 1000
);
```

## 🚀 功能特性

### 客服中心页面功能
1. **公司联系方式显示**
   - 自动从数据库加载公司地址、电话、邮箱
   - 支持加载状态显示
   - 点击电话号码可直接拨打

2. **客服留言功能**
   - 支持用户提交留言
   - 完整的表单验证（姓名、手机号、留言内容）
   - 手机号格式验证
   - 提交成功后清空表单

3. **在线客服按钮**
   - 浮动按钮设计
   - 支持企业微信客服（需配置）

## 📱 使用说明

### 用户端
1. 进入客服中心页面
2. 查看公司联系方式（地址、电话、邮箱）
3. 点击电话号码可直接拨打
4. 填写留言表单提交客服留言
5. 点击浮动按钮进行在线对话

### 管理端
1. 通过API管理公司信息
2. 查看和处理用户留言
3. 回复用户留言

## 🔧 API接口

### 获取公司信息
```
GET /api/company/info
```

### 提交客服留言
```
POST /api/system/feedback
{
  "name": "用户姓名",
  "contact": "手机号",
  "message": "留言内容",
  "userId": "用户ID（可选）"
}
```

## ✅ 验证步骤

1. 执行SQL命令创建表
2. 重启小程序后端服务
3. 在小程序中访问客服中心页面
4. 验证公司信息是否正确显示
5. 测试留言提交功能
6. 测试电话拨打功能

## 📝 注意事项

1. 确保数据库表已正确创建
2. 公司信息可通过API进行管理和更新
3. 用户留言会保存到数据库，可通过管理后台查看
4. 电话拨打功能需要用户授权
5. 在线客服功能需要配置企业微信相关参数

现在客服中心页面已经完全从数据库读取公司联系方式，并支持完整的留言功能！
