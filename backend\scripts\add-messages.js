const db = require('../utils/db');

async function addMessages() {
  try {
    // 获取"水水"用户ID（通过手机号查找）
    const shuishui = await db.query('SELECT id FROM users WHERE phone = ?', ['18100181000']);
    if (!shuishui || shuishui.length === 0) {
      console.log('未找到"水水"用户');
      return;
    }
    const shuishuiId = shuishui[0].id;

    // 获取其他5个用户
    const targetUsers = ['猎一一', '陌七七', '企十一', '吴吴吴', '李李'];
    const placeholders = targetUsers.map(() => '?').join(',');
    const otherUsers = await db.query(
      `SELECT id, nickname FROM users WHERE nickname IN (${placeholders})`,
      targetUsers
    );

    if (otherUsers.length === 0) {
      console.log('未找到目标用户');
      return;
    }

    // 为每个用户添加一条私信
    for (const user of otherUsers) {
      const message = {
        senderId: shuishuiId,
        receiverId: user.id,
        content: `你好，${user.nickname}`,
        type: 'text',
        isRead: false,
        createTime: Date.now()
      };

      await db.query(
        'INSERT INTO messages (senderId, receiverId, content, type, isRead, createTime) VALUES (?, ?, ?, ?, ?, ?)',
        [message.senderId, message.receiverId, message.content, message.type, message.isRead, message.createTime]
      );

      console.log(`已添加与用户 ${user.nickname} 的私信`);
    }

    console.log('所有私信添加完成');
  } catch (error) {
    console.error('添加私信失败:', error);
  } finally {
    process.exit();
  }
}

addMessages(); 